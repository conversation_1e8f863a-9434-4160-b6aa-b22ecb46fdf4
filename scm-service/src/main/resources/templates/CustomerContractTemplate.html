<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Customer Contract Template</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            font-size: 10pt;
            color: black;
        }
        .newItem {
            background-color: #ffff00;
            height: 12pt;
            page-break-inside: avoid;
        }
        .priceUpdated {
            background-color: #90EE90;
            height: 12pt;
            page-break-inside: avoid;
        }
        .repeatedItem {
            height: 12pt;
            page-break-inside: avoid;
        }
        .color-box-newProduct {
            width: 10px;
            height: 10px;
            display: inline-block;
            background-color: #ffff00;
        }
        .color-box-priceUpdated {
            width: 10px;
            height: 10px;
            display: inline-block;
            background-color: #90EE90;
        }
        .container {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        td, th {
            padding: 5px;
            vertical-align: top;
        }
        th {
            background-color: #f0f0f0;
        }
        .section-title {
            font-weight: bold;
            font-size: 12pt;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>

<div class="container">
    <p class="section-title">SCHEDULE I: FORM OF WORK ORDER</p>
    <p style="text-align: center;"><i>(To be read as part and parcel of the original Agreement executed between the Parties)</i></p>

    <table style="margin-top: 20px;">
        <tr>
            <td style="width: 40%;"><b>Work Order Number:</b></td>
            <td>$data.workOrderData.workOrderId</td>
        </tr>
        <tr>
            <td><b>Name of the Customer:</b></td>
            <td>$data.vendorDetail.entityName</td>
        </tr>
        <tr>
            <td><b>Address of the Customer:</b></td>
            <td>
                #if($data.vendorDetail.vendorAddress.line1)$data.vendorDetail.vendorAddress.line1<br>#end
                #if($data.vendorDetail.vendorAddress.line2)$data.vendorDetail.vendorAddress.line2<br>#end
                #if($data.vendorDetail.vendorAddress.line3)$data.vendorDetail.vendorAddress.line3<br>#end
                #if($data.vendorDetail.vendorAddress.city)$data.vendorDetail.vendorAddress.city, #end
                #if($data.vendorDetail.vendorAddress.state)$data.vendorDetail.vendorAddress.state, #end
                #if($data.vendorDetail.vendorAddress.country)$data.vendorDetail.vendorAddress.country #end
                #if($data.vendorDetail.vendorAddress.zipcode)- $data.vendorDetail.vendorAddress.zipcode#end
            </td>
        </tr>
        <tr>
            <td><b>Effective Date:</b></td>
            <td>$data.dateTool.format('dd/MM/yyyy', $data.workOrderData.startDate)</td>
        </tr>
    </table>

    <p class="section-title">New Commercials Agreed (if any):</p>
    <table border="1">
        <thead>
        <tr>
            <th>S.No</th>
            <th>Article Description</th>
            <th>UOM</th>
            <th>Dispatch Location</th>
            <th>Delivery Location</th>
            <th>Previous Rate</th>
            <th>Revised Rate</th>
            <th>Tax Percentage</th>
        </tr>
        </thead>
        <tbody>
        #foreach($item in $data.vendorContractItemDataVOS)
        <tr class="#if($item.skuPriceState=='PRICE_UPDATE') priceUpdated #elseif($item.skuPriceState == 'NEW_ITEM') newItem #else repeatedItem #end">
            <td>$velocityCount</td>
            <td>#if($item.skuId && $item.skuId.name)$item.skuId.name#end</td>
            <td>#if($item.packagingData && $item.packagingData.uom)$item.packagingData.uom#end</td>
            <td>#if($item.dispatchLocation)$item.dispatchLocation#end</td>
            <td>#if($item.deliveryLocation)$item.deliveryLocation#end</td>
            <td>#if($item.currentPrice)$data.mathTool.roundTo(2, $item.currentPrice)#end</td>
            <td>#if($item.updatedPrice)$data.mathTool.roundTo(2, $item.updatedPrice)#end</td>
            <td>#if($item.taxPercentage)$data.mathTool.roundTo(2, $item.taxPercentage)#end</td>
        </tr>
        #end
        </tbody>
    </table>

    <ul style="margin-top: 15px;">
        <li><b>Please note that all the above-mentioned prices are exclusive of all taxes.</b></li>
        <li>The above prices are valid from Effective Date of this Agreement.</li>
        <li><span class="color-box-priceUpdated"></span> denotes price is updated from the previous contract.</li>
        <li><span class="color-box-newProduct"></span> denotes new product is added which was not in previous contract</li>
    </ul>

    <p><b>Other Terms and Conditions:</b></p>
    <ol type="a">
        <li>This Work Order is issued under and forms an integral part of the Agreement executed between the Parties.</li>
        <li>Any change in price or commercial terms shall be reflected in the "New Commercials Agreed" table above and must be mutually agreed in writing. No verbal commitments shall be binding.</li>
        <li>All deliverables and obligations under this Work Order shall be performed in accordance with the original Agreement, unless specifically amended herein.</li>
        <li>This Work Order shall be deemed legally binding upon digital signatures of both Parties, with full legal force as per applicable laws.</li>
        <li>Effective from 28 May 2025, all future communication regarding price changes shall be made exclusively through Work Orders.</li>
    </ol>

    <p class="section-title" style="margin-top: 40px;">Accepted:</p>
    <table style="margin-top: 20px;">
        <tr>
            <td><b>For $data.companyName</b></td>
        </tr>
        <tr>
            <td>Digitally Accepted By: $data.employeeBasicDetail.name</td>
        </tr>
        <tr>
            <td>IP: $data.workOrderData.woApprovalMetaData.authIpAddress</td>
        </tr>
        <tr>
            <td>Designation: $data.employeeBasicDetail.designation</td>
        </tr>
        <tr>
            <td>#if($data.woMetaData.approverSignedDate)Date: $data.woMetaData.approverSignedDate #end</td>
        </tr>
    </table>
</div>

</body>
</html>
