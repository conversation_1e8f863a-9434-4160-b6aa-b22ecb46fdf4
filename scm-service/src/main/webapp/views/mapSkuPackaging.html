<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s6">
                <h4>Search Product</h4>
                <div class="input-field">
                    <select ui-select2="selectOptions" ng-model="productId" data-ng-change="selectProduct(productId)" data-placeholder="Enter name of a product">
                        <option value="" disabled selected>Select a product</option>
                        <option ng-repeat="product in allProducts" value="{{product.id}}">{{product.name}}</option>
                    </select>
                </div>
            </div>
            <div class="col s6">
                <h4>Search SKU</h4>
                <div class="input-field">
                    <select ui-select2="selectOptions" ng-model="skuId" data-ng-change="selectSKU(skuId)" data-placeholder="Enter name of a SKU">
                        <option ng-repeat="sku in skuList" value="{{sku.skuId}}">{{sku.skuName}}</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row simpleDemo">
    <div class="col s6 overflow">
        <!--<div class="input-field">
           <input type="text" data-ng-model="toSearch" placeholder="Search Packaging">
        </div>-->
        <div ng-repeat="(listName, list) in definitions.lists" class="row">
            <div  data-ng-if="list.length>0" class="panel panel-info">
                <div class="panel-heading">
                    <h5 class="panel-title">{{listName}}</h5>
                </div>
                <ul dnd-list="list" dnd-drop="checkIfExists(list, item, index)">
                    <li ng-repeat="item in list | filter : searchPackaging"
                        ng-class="{red:item.packagingStatus=='IN_ACTIVE'}"
                        dnd-acceptedTypes = "listName.toLowerCase()"
                        dnd-draggable="item"
                        dnd-effect-allowed="copy">
                        <span>{{item.packagingName}}</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col s6">
        <div class="row white z-depth-2">
            <div class="row margin0">
                <div class="col s6">
                    <h6 class="left custom-h6">Drag & drop to add profiles</h6>
                </div>
                <div class="col s6">
                    <button class="right btn margin-top-10" data-ng-click="submit()">Add to SKU</button>
                </div>
            </div>

            <div class="row margin0">
                <ul dnd-list="selectedProfiles" dnd-drop="onDrop(selectedProfiles, item, index)">
                    <li data-ng-if = "!checkEmpty(item)"
                        ng-repeat="item in selectedProfiles track by $index"
                        dnd-moved="selectedProfiles.splice($index, 1)"
                        dnd-effect-allowed="move"
                        dnd-selected="selectedProfiles.selected = item"
                        ng-class="{'selected': selectedProfiles.selected === item}">
                        <div class="row margin0">
                            <span class="left">{{item.packagingName}}</span>
                            <button class="right removeBtn btn"
                                    ng-click="removeFromSelected(selectedProfiles, item.packagingId)">Remove</button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row white z-depth-2" data-ng-if ="!checkEmpty(mappedProfiles)">
            <ul id="packagingView" class="collection with-header">
                <li class="collection-item active">Added Profiles to SKU</li>
                <li class="collection-item" data-ng-if = "!checkEmpty(item)"
                    ng-repeat="item in mappedProfiles track by $index">
                    <div class="row margin0">
                        <span class="left">{{getProfileName(item.packagingId)}}</span>
                        <div class="right">
                            <button class="btn btn-small removeBtn right" data-ng-if="item.mappingStatus=='ACTIVE'"
                                    ng-click="updateStatus(item.skuPackagingMappingId,false)">Disable</button>
                            <button class="btn btn-small removeBtn right" data-ng-if="item.mappingStatus=='IN_ACTIVE'"
                                    ng-click="updateStatus(item.skuPackagingMappingId,true)">Enable</button>
                            <button data-ng-if="!item.isDefault" class="btn btn-small removeBtn right margin-right-5"
                                    data-ng-click="markDefault(item.skuPackagingMappingId)">Set Default</button>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>


