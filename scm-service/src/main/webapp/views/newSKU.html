<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-ng-init="init()">
	<div
		id="skuDef"
		data-ng-model="skuDefinition"
		class="row scm-form">
		<div class="row">
			<div class="col s6">
				<div class="left">
					<h5 style="margin: 0px;">SKU Definition</h5>
				</div>
			</div>
			<div
				data-ng-if="editMode && empType != 'USER'"
				class="col s6">
				<div class="right">
					<button
						class="btn"
						data-ng-click="goBack(skuDefinition.skuId)">Back</button>
				</div>
			</div>
		</div>
		<form
			name="skuForm"
			class="white z-depth-3 scm-form"
			novalidate>
			<div class="row">
				<div class="col s12 m6 l6">
					<label
						class="black-text active"
						for="product">Linked Product</label>
					<div
						class="form-element"
						data-ng-if="!editMode">
						<select
								id="product"
							ui-select2
							data-ng-model="linkedProduct"
							data-ng-change="setAttributeMapping(linkedProduct)"
							data-placeholder="Select a product"
							data-ng-options="product as product.name for product in allProducts"
							required>
							<!-- <option
								data-ng-repeat="product in products"
								value="{{product}}">{{product.productName}} -
								[{{product.productCode}}]</option> -->
						</select>
						<p
							data-ng-show="skuForm.product.$error.required"
							class="errorMessage">Product name is required.
						</p>
					</div>
					<div data-ng-if="linkedProduct.categoryDefinition.id == 3 && (empType != 'USER' && empType != null)" ng-form="attrProfile">
						<div data-ng-repeat="entity in skuDefinition.entityAttributeValueMappings track by entity.profileAttributeMappingId">
							<label class="black-text active" for="{{entity.attributeId}}">{{getAttributeName(entity.attributeId)}} :</label>
							<div data-ng-if="!entity.isMandatory">
								<select id="{{entity.attributeId }}"  name="{{entity.attributeId}}"
										data-ng-model="entity.attributeValueId" data-ng-change="generateSKUName()"
										>
									<option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
								</select>
								<!--data-ng-options="value.attributeValueId as value.attributeValue for value in entity.valueList"-->
							</div>
							<div data-ng-if="entity.isMandatory">
								<select id="{{entity.attributeId}}"  name="{{entity.attributeId}}"
										data-ng-model="entity.attributeValueId" data-ng-change="generateSKUName()"
										 required>
									<option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
								</select>
								<p ng-show="entity.attributeValueId == null" class="errorMessage">Please Select a value</p>
							</div>
							<a ng-if="entity.associatedImage!=NULL" href="{{'#' + entity.attributeId + '_image'}}" modal acl-action="RECPRA">
								<i class="fa fa-eye black-text" aria-hidden="true"></i>
							</a>
							<div class="modal" id="{{entity.attributeId + '_image'}}" style="width:72%; max-height:80%; overflow:visible;">
								<div class="frame">
									<img style="width:731px; height:300px;" data-ng-if="entity.associatedImage != null" data-ng-src="{{profileBaseUrl}}{{entity.associatedImage}}" />
								</div>
								<div class="col s2 form-element">
									<a class="modal-action modal-close waves-effect btn" href="javascript:void(0)" >BACK </a>
								</div>
							</div>
						</div>
					</div>
					<div class="form-element" style="margin-top: 16px;">
						<input
							type="text"
							data-ng-if="editMode"
							data-ng-model="linkProductNameCode"
							disabled></input>
					</div>

					<div data-ng-if="linkedProduct.categoryDefinition.id != 3 && (empType != 'USER' && empType != null)"
						class="form-element"
						data-ng-repeat="mapping in categoryAttributeMappings track by $index">
						<label for="{{mapping.attributeDefinition.name.replace('/','')}}">Select
							{{mapping.attributeDefinition.name}}</label>
						<select
							id="{{mapping.attributeDefinition.name.replace('/','')}}"
							ui-select2="{allowClear:true}"
							data-ng-model="mapping.selectedAttribute"
							data-placeholder="Select a value"
							data-ng-change="generateSKUName()"
							data-ng-options="attrValue as attrValue.attributeValue for attrValue in mapping.attributeValues track by attrValue.attributeValueId">
							<!-- <option
								data-ng-repeat="attrValue in mapping.attributeValues track by attrValue.attributeValueId"
								value="{{attrValue}}">{{attrValue.attributeValue}}</option> -->
						</select>
					</div>
					<!--<div-->
							<!--class="form-element"-->
							<!--data-ng-repeat="mapping in selectedProfileAttributeMappings track by $index">-->
						<!--<label for="{{mapping.attributeDefinition.name}}">Select-->
							<!--{{mapping.attributeDefinition.name}}</label>-->
						<!--<select-->
								<!--id="{{mapping.attributeDefinition.name.replace('/','')}}"-->
								<!--ui-select2="selectOptions"-->
								<!--data-ng-model="mapping.selectedAttribute"-->
								<!--data-placeholder="Select a value"-->
								<!--data-ng-change="generateSKUName()"-->
								<!--data-ng-options="attrValue as attrValue.attributeValue for attrValue in mapping.attributeValues track by attrValue.attributeValueId">-->
							<!--&lt;!&ndash; <option-->
								<!--data-ng-repeat="attrValue in mapping.attributeValues track by attrValue.attributeValueId"-->
								<!--value="{{attrValue}}">{{attrValue.attributeValue}}</option> &ndash;&gt;-->
						<!--</select>-->
					<!--</div>-->
					<div class="form-element" data-ng-if="editMode">
						<label
							class="black-text"
							for="skuName">Original SKU Name </label>
						<input
							id="skuNameO"
							data-ng-model="skuDefinition.skuName"
							type="text"
							disabled>
					</div>
					<div class="form-element">
						<label
							class="black-text"
							for="skuName">SKU Name </label>
						<input
							id="skuName"
							data-ng-model="generatedSkuName"
							type="text">
					</div>
					<div class="form-element" data-ng-if="skuDefinition!=undefined && skuDefinition!=null && empType != 'USER'">
						<label
							class="black-text active"
							for="torqusSkuName">Torqus SKU Name
						</label>
						<input
							type="text"
							id="torqusSkuName"
							name="torqusName"
							data-ng-model="skuDefinition.torqusSkuName"
							data-ng-maxlength="100" disabled />
						<p
							data-ng-show="skuForm.torqusName.$error.maxlength"
							class="errorMessage">Torqus name is too large.
						</p>
					</div>
					<div class="form-element">
						<label
							class="black-text"
							for="description">SKU Description</label>
						<textarea
							id="description"
							name="description"
							data-ng-model="skuDefinition.skuDescription"
							data-ng-maxlength="1000"
							required></textarea>
						<p
							data-ng-show="skuForm.description.$error.required"
							class="errorMessage">Description is required.</p>
						<p
							data-ng-show="skuForm.description.$error.maxlength"
							class="errorMessage">Description is too large.</p>
					</div>
				</div>
				<div class="col s12 m6 l6">
					<div class="form-element">
						<label
							class=" black-text active"
							for="uom">Unit of Measurement</label>
						<input
							id="uom"
							name="uom"
							data-ng-model="skuDefinition.unitOfMeasure"
							type="text"
							disabled
							required data-ng-disabled="empType == 'USER'"/>
						<p
							data-ng-show="skuForm.uom.$error.required"
							class="errorMessage">Unit of measure is required.</p>
					</div>
					<div class="form-element">
						<label class="black-text active" for="taxCategory">Tax Category</label>
						<select ui-select2 id="taxCategory" name="taxCategory" data-ng-model="taxCategory"
								data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in taxCodes track by tax.code" data-ng-change="onTaxCategoryChange()"
								required data-ng-disabled="empType == 'USER'">
						</select>
						<a data-ng-show="taxCategory != null" data-ng-click="showTaxDetail()">?</a>
						<p ng-show="skuForm.taxCategory.$error.required" class="errorMessage">SKU Tax Category is required.</p>
					</div>
					<div class="form-element">
						<label
							class="black-text active"
							for="shelfLifeInDays">Shelf Life(in days)</label>
						<input
							type="number"
							ng-disabled="(linkedProduct != null && linkedProduct.shelfLifeInDays == -1) || empType == 'USER'"
							id="shelfLifeInDays"
							name="shelfLifeInDays"
							data-ng-model="skuDefinition.shelfLifeInDays"
							data-ng-maxlength="5"
							min="-1"
							required/>
						<p
							data-ng-show="skuForm.shelfLifeInDays.$error.maxlength"
							class="errorMessage">SKU shelf life is too large.</p>
						<p
							data-ng-show="skuForm.shelfLifeInDays.$error.required"
							class="errorMessage">SKU shelf life is required.</p>
						<p
							data-ng-show="skuForm.shelfLifeInDays.$error.min"
							class="errorMessage">SKU shelf life is too small.</p>
					</div>
					<div class="form-element" data-ng-if="empType != 'USER'">
						<label
							class="black-text active"
							for="inventoryList">Inventory List</label>
						<select
							id="inventoryList"
							name="inventoryList"
							data-ng-disabled="linkedProduct != null
								&& linkedProduct.categoryDefinition != null
								&& linkedProduct.categoryDefinition.id == 3"
							data-ng-model="skuDefinition.inventoryList"
							data-ng-options="list.id as list.name for list in inventoryList"
							data-placeholder="Select a InventoryList"
							required>
						</select>
						<p
							data-ng-show="skuForm.inventoryList.$error.required"
							class="errorMessage">Inventory is required.</p>
					</div>
					<!--<div class="form-element" data-ng-if="!editMode">-->
						<!--<label-->
							<!--class="black-text"-->
							<!--for="unitPrice">Unit Price</label>-->
						<!--<input-->
							<!--id="unitPrice"-->
							<!--name="unitPrice"-->
							<!--data-ng-maxlength="10"-->
							<!--min="0"-->
							<!--data-ng-model="skuDefinition.unitPrice"-->
							<!--type="number"-->
							<!--required />-->
						<!--<p-->
							<!--data-ng-show="skuForm.unitPrice.$error.required"-->
							<!--class="errorMessage">SKU unit price is required.</p>-->
						<!--<p-->
							<!--data-ng-show="skuForm.unitPrice.$error.maxlength"-->
							<!--class="errorMessage">SKU unit price is too large.</p>-->
						<!--<p-->
							<!--data-ng-show="skuForm.unitPrice.$error.min"-->
							<!--class="errorMessage">SKU unit price is too small.</p>-->
					<!--</div>-->
					<!--<div class="form-element" data-ng-if="!editMode">-->
						<!--<label-->
							<!--class="black-text"-->
							<!--for="negotiatedUnitPrice">Negotiated Unit Price</label>-->
						<!--<input-->
							<!--id="negotiatedUnitPrice"-->
							<!--name="negotiatedUnitPrice"-->
							<!--data-ng-maxlength="10"-->
							<!--min="0"-->
							<!--data-ng-model="skuDefinition.negotiatedUnitPrice"-->
							<!--type="number"-->
							<!--required />-->
						<!--<p-->
							<!--data-ng-show="skuForm.negotiatedUnitPrice.$error.required"-->
							<!--class="errorMessage">SKU negotiated unit price is required.</p>-->
						<!--<p-->
							<!--data-ng-show="skuForm.negotiatedUnitPrice.$error.maxlength"-->
							<!--class="errorMessage">SKU negotiated unit price is too large.</p>-->
						<!--<p-->
							<!--data-ng-show="skuForm.negotiatedUnitPrice.$error.min"-->
							<!--class="errorMessage">SKU negotiated unit price is too small.</p>-->
					<!--</div>-->
					<div data-ng-if="empType != 'USER'">
						<div class="form-element">
							<input
								id="looseOrdering"
								data-ng-model="skuDefinition.supportsLooseOrdering"
								type="checkbox" />
							<label
								class="black-text"
								for="looseOrdering">Loose Ordering</label>
						</div>
						<div class="form-element">
							<input
								id="hasCase"
								data-ng-model="skuDefinition.hasCase"
								type="checkbox" />
							<label
								class="black-text"
								for="hasCase">Has Case</label>
						</div>
						<div class="form-element">
							<input
								id="hasInner"
								data-ng-model="skuDefinition.hasInner"
								type="checkbox" />
							<label
								class="black-text"
								for="hasInner">Has Inner</label>
						</div>
						<div class="form-element">
							<input
								id="isDefault"
								data-ng-model="skuDefinition.isDefault"
								type="checkbox" />
							<label
								class="black-text active"
								for="isDefault">Is default</label>
						</div>
						<div class="form-element" data-ng-show="displayIsBranded">
							<input
									id="isBranded"
									data-ng-model="skuDefinition.isBranded"
									type="checkbox" />
							<label
									class="black-text active"
									for="isBranded">Is Branded</label>
						</div>
						<div class="form-element">
							<label
									class="black-text active"
									for="voDiscontinuedFrom">Vendor Ordering Discontinued From</label>
							<input input-date type="text"
								id="voDisContinuedFrom"
								data-ng-model="skuDefinition.voDisContinuedFrom"
								container="" format="yyyy-mm-dd"
								data-ng-change="setVoDisContinuedFrom(skuDefinition.voDisContinuedFrom)"
							   min="{{minDisContinuedDate}}"/>
						</div>
						<div class="form-element">
							<label
									class="black-text active"
									for="roDisContinuedFrom">RO Discontinued From</label>
							<input input-date type="text"
								id="roDisContinuedFrom"
								data-ng-model="skuDefinition.roDisContinuedFrom"
								container="" format="yyyy-mm-dd"
								data-ng-change="setRoDisContinuedFrom(skuDefinition.roDisContinuedFrom)"
							   min="{{minDisContinuedDate}}"/>
						</div>
					</div>
				</div>
				<div class="col" data-ng-if="empType == 'USER'">
						<div class="row">
							<button class="btn waves-green" title="UPLOAD SKU IMAGE" data-ng-click="uploadDoc('IMAGE')">Upload Image</button>
							<button data-ng-if="documentId != null" title="DOWNLOAD SKU IMAGE" class="btn orange" style="margin-top: 5px; margin-bottom: 5px;" data-ng-click="downloadDoc(documentId)">Download Image</button>
						</div>
						<label data-ng-show="documentId == null" class="errorMessage">Please Upload SKU Image *</label>
						<div class="row">
							<button class="btn waves-green" style="margin-top: 5px; margin-bottom: 5px;" title="UPLOAD SKU APPROVAL DOCUMENT" data-ng-click="uploadDoc('OTHER')">Upload Doc</button>
							<button data-ng-if="otherDocumentId != null" title="DOWNLOAD SKU APPROVAL DOCUMENT" class="btn orange" style="margin-top: 5px; margin-bottom: 5px;" data-ng-click="downloadDoc(otherDocumentId)">Download Doc</button>
						</div>
						<label data-ng-show="otherDocumentId == null" class="errorMessage">Please Upload SKU Approval Document *</label>
				</div>
				<div data-ng-if="empType != null && empType != 'USER'">
					<div data-ng-if="skuDefinition.approvalDocId != null">
						<label>SKU Approval Document</label>
						<input type="button" value="Approval DOC" data-ng-click="downloadDoc(skuDefinition.approvalDocId)" class="waves-effect waves-green btn"  style="background-color: orange; color: white;"/>
					</div>
					<div data-ng-if="skuDefinition.skuImage != null">
						<label>SKU Image</label>
						<input type="button" value="Sku Image" data-ng-click="downloadDoc(skuDefinition.skuImage)" class="waves-effect waves-green btn"  style="background-color: orange; color: white;"/>
					</div>
				</div>
			</div>
		</form>
		<div
			class="row"
			style="margin-top: 10px;">
			<div class="col s12">
				<button
					data-ng-if="!editMode && skuForm.$valid && empType == null"
					data-ng-click="createSKU()"
					class="btn right" acl-action="SPAASD">
					Create <i class="material-icons right">send</i>
				</button>
			</div>
		</div>
		<div
			class="row"
			style="margin-top: 10px;">
			<div class="col s12">
				<button
					data-ng-if="editMode && skuForm.$valid && empType == null"
					data-ng-click="createSKU()"
					class="btn right" acl-action="SPAUSP">
					Update <i class="material-icons right">send</i>
				</button>
			</div>
		</div>
		<div>
			<button data-ng-if="skuForm.$valid && empType == 'USER' && documentId != null && otherDocumentId != null" data-ng-click="createSKU()" class="btn waves-green right waves-effect" >CREATE</button>
			<button data-ng-if="skuForm.$valid && empType == 'FINANCE'" data-ng-click="createSKU('APPROVE')" class="btn waves-green right waves-effect" >APPROVE SKU</button>
			<button data-ng-if="skuForm.$valid && empType == 'FINANCE'" data-ng-click="createSKU('REJECT')" style="gap: 20px;" class="btn red waves-effect right" >REJECT SKU</button>
		</div>
	</div>
</div>
