

<head>
    <link rel="stylesheet" href="css/multiselect.css">
    <style>
        .selectors-row {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: flex-end;
        }
        .selectors-row .form-group {
            flex: 1;
            min-width: 200px;
        }
        .selectors-row .search-btn {
            padding-bottom: 10px;
        }
        .mismatch-row {
            background-color: #98ed9b !important;
        }
        .narrow-select {
            max-width: 320px;
        }
    </style>
</head>
<div class="container" data-ng-init="init()">
    <h4>Region Product Packaging Mapping</h4>

    <div class="selectors selectors-row">
        <!-- REGIONS MULTISELECT -->
        <div class="form-group">
            <label class="form-label"><i class="material-icons tiny">domain</i> Regions</label>
            <div id="regions" style="text-align: left; position: relative;"
                 ng-dropdown-multiselect=""
                 options="regionsDropdown" selected-model="selectedRegions"
                 extra-settings="multiSelectSettings1"
                 translation-texts="{buttonDefaultText: 'Select Regions'}">
            </div>
        </div>

        <!-- PRODUCTS MULTISELECT -->
        <div class="form-group">
            <label class="form-label"><i class="material-icons tiny">domain</i> Products*</label>
            <div id="products" style="text-align: left; position: relative;"
                 ng-dropdown-multiselect=""
                 options="allProducts" selected-model="selectedProducts"
                 extra-settings="multiSelectSettings2"
                 translation-texts="{buttonDefaultText: 'Select Products'}">
            </div>
        </div>

        <!-- SEARCH BUTTON -->
        <div class="form-group search-btn">
            <button class="btn btn-primary" ng-click="searchMappings()" data-ng-disabled="selectedProducts.length == 0">
                Search
            </button>
        </div>
    </div>

    <button class="btn btn-primary right" ng-click="verifyAndSubmitChanges()" ng-if="regionProductPackagingData.length">Submit</button>

    <table ng-if="regionProductPackagingData.length">
        <thead>
        <tr>
            <th>Region</th>
            <th>
                <input type="text"
                   ng-model="searchProductName"
                   placeholder="Search Product"
                   class="form-control"
                   style="max-width: 200px;">
            </th>
            <th>Old Packaging</th>
            <th>New Packaging</th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="rpm in paginatedList | filter:{productName: searchProductName}"
            ng-class="{'mismatch-row': (rpm.packagingId !== rpm.oldPackagingId)}">
            <td>{{ rpm.regionCode }}</td>
            <td>{{ rpm.productName }}</td>
            <td>{{ rpm.packagingName }}</td>
            <td>
                <select ui-select2 type="text" pattern=".*\S+.*" id="packagingId" class="form-control narrow-select"
                        ng-model="rpm.packagingId"
                        data-ng-options="p.id as p.name for p in rpm.packagings">
                </select>
            </td>
        </tr>
        </tbody>
    </table>
<!--    <div class="pagination-controls" ng-if="filteredData.length > itemsPerPage" style="margin-top: 12px;">-->
<!--        <button class="btn btn-flat" ng-disabled="currentPage === 1" ng-click="previousPage()">Previous</button>-->
<!--        <span style="margin: 0 8px;">Page {{ currentPage }} of {{ pageCount() }}</span>-->
<!--        <button class="btn btn-flat" ng-disabled="currentPage >= pageCount()" ng-click="nextPage()">Next</button>-->
<!--    </div>-->
    <div class="col s12" style="margin-top: 10px;" data-ng-if="regionProductPackagingData.length">
        <ul class="pagination center-align">
            <li class="{{currentPage === 1 ? 'disabled' : 'waves-effect'}}">
                <a href="" data-ng-click="currentPage > 1 && (currentPage = currentPage - 1) && updatePagination()">
                    <i class="material-icons">chevron_left</i>
                </a>
            </li>
            <li class="active"><a href="">{{currentPage}}</a></li>
            <li class="{{(currentPage * pageSize) >= regionProductPackagingData.length ? 'disabled' : 'waves-effect'}}">
                <a href="" data-ng-click="(currentPage * pageSize) < regionProductPackagingData.length && (currentPage = currentPage + 1) && updatePagination()">
                    <i class="material-icons">chevron_right</i>
                </a>
            </li>
            <p class="center-align grey-text">
                Total: {{regionProductPackagingData.length}} entries
            </p>
        </ul>
    </div>
</div>

</body>
</html>
