<link rel="stylesheet" href="css/vendorContract.css">
<div class="row" data-ng-init="init()" style="margin-bottom: 0; overflow: hidden;">
    <div class="col s12" style="overflow-x: hidden;">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col s12">
                        <h4 style="margin: 0; font-weight: 400; color: var(--text-color);">Vendor Contract Management</h4>
                        <p style="margin-top: 5px; color: var(--text-light);">Search, view and manage vendor contracts</p>
                    </div>
                </div>
            </div>

            <!-- Search Form -->
            <div class="card-content search-form">
                <div class="row">
                    <div class="col s12 m6 l2">
                        <label>Start Date</label>
                        <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" class="browser-default"/>
                    </div>
                    <div class="col s12 m6 l2">
                        <label>End Date</label>
                        <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" class="browser-default"/>
                    </div>
                    <div class="col s12 m6 l2">
                        <label>Status</label>
                        <select data-ng-model="selectedStatus" class="browser-default"
                                data-ng-options="type as type for type in contractStatus"></select>
                    </div>
                    <div class="col s12 m6 l2">
                        <label>Vendor</label>
                        <select ui-select2
                                style="width: 100%;"
                                id="vendors"
                                data-ng-model="vendorSelected"
                                data-ng-options="vendor as (vendor.name + ' [' + vendor.category + ']') for vendor in allVendorDataList | orderBy : 'name' track by vendor.id"
                                data-ng-change="selectVendor(vendorSelected)"
                                required></select>
                        <p data-ng-show="basicDetail.selectVendorValue.$error.required"
                           class="errorMessage" style="color: var(--danger-color); font-size: 12px; margin-top: 5px;">Please select vendor.</p>
                    </div>
                    <div class="col s12 m6 l2">
                        <label>Contract ID</label>
                        <input type="number" class="browser-default" ng-model="vendorContractId" ng-change="bypassContractIdChange(vendorContractId)"/>
                    </div>
                    <div class="col s12 m6 l2">
                        <button class="btn waves-effect waves-light" data-ng-click="getContracts()">Search</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Results Message -->
        <div class="card" data-ng-if="contractRequest.length==0">
            <div class="card-content center-align" style="padding: 40px 20px;">
                <i class="material-icons" style="font-size: 48px; color: #9e9e9e; display: block; margin-bottom: 10px;">search</i>
                <p style="color: #757575; font-size: 18px;">No contracts found for the selected criteria</p>
                <p style="color: #9e9e9e; font-size: 14px; margin-top: 10px;">Try adjusting your search parameters</p>
            </div>
        </div>

        <!-- Contract List -->
        <div class="card" ng-if="contractRequest.length>0">
            <div class="card-header">
                <div class="row">
                    <div class="col s6">
                        <h5 style="margin: 0; font-weight: 500; color: var(--text-color);">Contract List</h5>
                    </div>
                    <div class="col s6 right-align">
                        <span class="chip" style="background-color: var(--background-light); color: var(--text-color);">
                            <i class="material-icons left" style="font-size: 16px;">filter_list</i>
                            {{contractRequest.length}} Results
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <!-- Contract Accordion -->
                <ul class="collapsible" data-collapsible="accordion" watch>
                    <li class="contract-item" data-ng-repeat="invR in contractRequest track by $index">
                        <!-- Contract Header -->
                        <div class="collapsible-header waves-effect" style="padding: 15px; display: block;" ng-click="getWorkOrdersByContractId(invR)">
                            <div class="row" style="margin-bottom: 0; display: flex; flex-wrap: wrap;">
                                <div class="col s12 m8" style="min-width: 0;">
                                    <div style="display: flex; align-items: center; min-width: 0;">
                                        <i class="material-icons" style="margin-right: 10px; color: var(--primary-color); flex-shrink: 0;">description</i>
                                        <div style="min-width: 0; overflow: hidden;">
                                            <h5 style="margin: 0; font-size: 16px; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{invR.vendorType}} #{{invR.contractId}}</h5>
                                            <p style="margin: 5px 0 0 0; color: var(--text-light); font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Vendor: {{invR.vendorUserName}}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col s12 m4">
                                    <!-- Status and Bypass Container -->
                                    <div style="display: flex; flex-wrap: wrap; justify-content: flex-end; align-items: center; margin-bottom: 5px;">
                                        <span class="status-badge" style="margin-right: 5px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 320px;"
                                              ng-class="{
                                                'active': invR.status === 'APPLIED' || invR.status === 'APPROVER_BY_PASSED',
                                                'pending': invR.status === 'PENDING_VENDOR_APPROVAL' || invR.status === 'VENDOR_APPROVED' || invR.status === 'PARTIALLY_REJECTED' || invR.status === 'CREATED',
                                                'rejected': invR.status === 'VENDOR_REJECTED' || invR.status === 'APPROVER_REJECTED' || invR.status === 'CANCELLED',
                                                'inactive': invR.status === 'EXPIRED' || invR.status === 'DEACTIVATED'
                                              }">
                                            {{invR.status}}
                                        </span>
                                        <span data-ng-show="invR.isByPassed == 'Y'" class="chip orange white-text" style="margin: 0; white-space: nowrap;" title="The contract has been by-passed by APPROVER">
                                            <i class="material-icons" style="font-size: 16px; margin-right: 4px; float: left;">fast_forward</i>Bypassed
                                        </span>
                                    </div>

                                    <!-- Creator and Date Container -->
                                    <div style="display: flex; flex-direction: column; align-items: flex-end;">
                                        <div style="margin-bottom: 5px; text-align: right;">
                                            <span class="chip grey white-text" style="margin: 2px; max-width: 100%; overflow: hidden; text-overflow: ellipsis;">
                                                <i class="material-icons" style="font-size: 16px; margin-right: 4px; float: left;">person</i>
                                                <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block; max-width: 220px;">{{invR.createdByName}}</span>
                                            </span>
                                        </div>
                                        <div style="text-align: right;">
                                            <span class="chip grey white-text" style="margin: 2px; max-width: 100%; overflow: hidden; text-overflow: ellipsis;">
                                                <i class="material-icons" style="font-size: 16px; margin-right: 4px; float: left;">date_range</i>
                                                <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block;">{{invR.startDate | date : 'yyyy-MM-dd'}} to {{invR.endDate | date : 'yyyy-MM-dd'}}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contract Body -->
                        <div class="collapsible-body" style="padding: 0;">
                            <!-- Work Orders Section -->
                            <div class="hierarchy-container" style="padding: 20px;">
                                <h6 style="margin-top: 0; font-weight: 500; color: var(--text-color); margin-bottom: 15px;">
                                    <i class="material-icons left" style="font-size: 20px; color: var(--primary-color);">assignment</i>
                                    Work Orders
                                </h6>

                                <!-- Loading Message -->
                                <div class="center-align" ng-if="!workOrders && !workOrdersLoaded" style="padding: 20px;">
                                    <div class="preloader-wrapper small active">
                                        <div class="spinner-layer spinner-blue-only">
                                            <div class="circle-clipper left">
                                                <div class="circle"></div>
                                            </div>
                                            <div class="gap-patch">
                                                <div class="circle"></div>
                                            </div>
                                            <div class="circle-clipper right">
                                                <div class="circle"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <p style="margin-top: 10px; color: var(--text-light);">Loading work orders...</p>
                                </div>

                                <!-- No Work Orders Message -->
                                <div class="center-align" ng-if="workOrders && workOrders.length === 0" style="padding: 20px;">
                                    <i class="material-icons" style="font-size: 48px; color: #9e9e9e; display: block; margin-bottom: 10px;">assignment_late</i>
                                    <p style="color: #757575; font-size: 16px;">No work orders found for this contract</p>
                                </div>

                                <!-- Work Order List -->
                                <div class="hierarchy-line" ng-if="workOrders && workOrders.length > 0"></div>
                                <div class="hierarchy-item" ng-repeat="wo in workOrders" ng-if="workOrders && workOrders.length > 0">
                                    <div class="hierarchy-dot"></div>

                                    <!-- Work Order Header -->
                                    <div class="work-order-header"
										ng-click="getItemsByWoId(wo);"
										ng-class="{'active': wo.expanded, 'light-red': ['CANCELLED', 'VENDOR_REJECTED', 'REMOVED_ALL'].includes(wo.workOrderStatus)}">
                                        <div class="row" style="margin-bottom: 0; display: flex; flex-wrap: wrap;">
                                            <div class="col s12 m8" style="min-width: 0;">
                                                <div style="min-width: 0; overflow: hidden;">
                                                    <h6 style="margin: 0; font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Work Order #{{wo.workOrderId}}</h6>
                                                    <p style="margin: 5px 0 0 0; color: var(--text-light); font-size: 13px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Type: {{wo.workOrderType}}</p>
                                                </div>
                                            </div>
                                            <div class="col s12 m4">
                                    <!-- Status and Bypass Container -->
                                    <div style="display: flex; flex-wrap: wrap; justify-content: flex-end; align-items: center; margin-bottom: 5px;">
                                        <span class="status-badge" style="margin-right: 5px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 320px;"
                                              ng-class="{
                                                'active': wo.workOrderStatus === 'APPLIED' || wo.workOrderStatus === 'APPROVER_BY_PASSED',
                                                'pending': wo.workOrderStatus === 'PENDING_VENDOR_APPROVAL' || wo.workOrderStatus === 'VENDOR_APPROVED' || wo.workOrderStatus === 'PARTIALLY_REJECTED' || wo.workOrderStatus === 'CREATED',
                                                'rejected': wo.workOrderStatus === 'VENDOR_REJECTED' || wo.workOrderStatus === 'APPROVER_REJECTED' || wo.workOrderStatus === 'CANCELLED',
                                                'inactive': wo.workOrderStatus === 'EXPIRED' || wo.workOrderStatus === 'DEACTIVATED'
                                              }">
                                            {{wo.workOrderStatus}}
                                        </span>
                                        <span data-ng-show="wo.isByPassed == 'Y'" class="chip orange white-text" style="margin: 0; white-space: nowrap;" title="The work order has been by-passed by APPROVER">
                                            <i class="material-icons" style="font-size: 16px; margin-right: 4px; float: left;">fast_forward</i>Bypassed
                                        </span>
                                    </div>

                                    <!-- Creator and Date Container -->
                                    <div style="display: flex; flex-direction: column; align-items: flex-end;">
                                        <div style="margin-bottom: 5px; text-align: right;">
                                            <span class="chip grey white-text" style="margin: 2px; max-width: 100%; overflow: hidden; text-overflow: ellipsis;">
                                                <i class="material-icons" style="font-size: 16px; margin-right: 4px; float: left;">person</i>
                                                <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block; max-width: 220px;">{{wo.createdByName}}</span>
                                            </span>
                                        </div>
                                        <div style="text-align: right;">
                                            <span class="chip grey white-text" style="margin: 2px; max-width: 100%; overflow: hidden; text-overflow: ellipsis;">
                                                <i class="material-icons" style="font-size: 16px; margin-right: 4px; float: left;">date_range</i>
                                                <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block;">{{wo.startDate | date : 'yyyy-MM-dd'}} to {{wo.endDate | date : 'yyyy-MM-dd'}}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                    </div>

                                    <!-- Work Order Body (Items) -->
                                    <div class="work-order-body" ng-class="{'active': wo.expanded}" data-ng-if="wo.expanded">
                                        <!-- Loading Message -->
                                        <div class="center-align" ng-if="!wo.items && !wo.itemsLoaded" style="padding: 20px;">
                                            <div class="preloader-wrapper small active">
                                                <div class="spinner-layer spinner-blue-only">
                                                    <div class="circle-clipper left">
                                                        <div class="circle"></div>
                                                    </div>
                                                    <div class="gap-patch">
                                                        <div class="circle"></div>
                                                    </div>
                                                    <div class="circle-clipper right">
                                                        <div class="circle"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <p style="margin-top: 10px; color: var(--text-light);">Loading items...</p>
                                        </div>

                                        <!-- No Items Message -->
                                        <div class="center-align" ng-if="wo.items && wo.items.length === 0" style="padding: 20px;">
                                            <i class="material-icons" style="font-size: 36px; color: #9e9e9e; display: block; margin-bottom: 10px;">inventory_2</i>
                                            <p style="color: #757575; font-size: 14px;">No items found for this work order</p>
                                        </div>

										<!-- Work Order Actions -->
										<div class="row" style="margin: 0; padding: 15px; background-color: #f9f9f9; border-bottom: 1px solid #e0e0e0;" ng-if="wo.expanded">
											<div class="col s12">
												<!-- Cancel Contract Button -->
												<button class="btn waves-effect waves-light red"
														data-ng-if="showCancelBtn(wo.workOrderStatus) && canCancelWo(invR.vendorType)"
														data-ng-click="cancelContract(wo.workOrderId)" title="CANCEL WORK ORDER">
													<i class="material-icons left">cancel</i>Cancel WO
												</button>

												<!-- Copy URL Button -->
												<button class="btn waves-effect waves-light blue"
														data-ng-if="wo.workOrderStatus == 'PENDING_VENDOR_APPROVAL' && wo.workOrderType == 'DEFAULT'"
														data-ng-click="copyVendorApprovalPage(wo.vendorApprovalLink)" title="VENDOR APPROVAL PAGE URL">
													<i class="material-icons left">content_copy</i>Copy URL
												</button>

												<!-- Contract Document Buttons -->
												<button class="btn waves-effect waves-light orange"
														data-ng-if="wo.unsignedDocumentId!=null && wo.authSignedDocId==null && wo.vendorSignedDocId==null"
														data-ng-click="printContract(wo.unsignedDocumentId, $index)" title="UNSIGNED CONTRACT">
													<i class="material-icons left">description</i>Unsigned Contract
												</button>

												<button class="btn waves-effect waves-light orange"
														data-ng-if="wo.vendorSignedDocId!=null && wo.authSignedDocId==null"
														data-ng-click="printContract(wo.vendorSignedDocId, $index)" title="VENDOR SIGNED CONTRACT">
													<i class="material-icons left">description</i>Vendor Signed Contract
												</button>

												<button class="btn waves-effect waves-light orange"
														data-ng-if="wo.authSignedDocId!=null"
														data-ng-click="printContract(wo.authSignedDocId, $index)" title="AUTH SIGNED CONTRACT">
													<i class="material-icons left">description</i>Auth Signed Contract
												</button>

												<!-- Contract Doc Button -->
												<button class="btn waves-effect waves-light purple"
														data-ng-if="wo.workOrderDocId != null"
														data-ng-click="downloadDoc(wo.workOrderDocId)" title="VENDOR CONTRACT DOCUMENT">
													<i class="material-icons left">cloud_download</i>Download Document
												</button>
											</div>
										</div>

                                        <!-- Items Table -->
                                        <table class="bordered striped" ng-if="wo.items && wo.items.length > 0">
                                            <thead>
                                                <tr>
                                                    <th>SKU</th>
                                                    <th>UOM</th>
                                                    <th>Packaging</th>
                                                    <th>Dispatch Location</th>
                                                    <th>Delivery Location</th>
                                                    <th>Price</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="item in wo.items">
                                                    <td>
                                                        <a data-ng-click="showPreview($event, item.skuId.id,'SKU')">{{item.skuId.name}} [{{item.skuId.id}}]</a>
                                                    </td>
                                                    <td>{{item.packagingData.uom}}</td>
                                                    <td>{{item.packagingData.name}}</td>
                                                    <td>{{item.dispatchLocation}}</td>
                                                    <td>{{item.deliveryLocation}}</td>
                                                    <td>{{item.updatedPrice}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

