<head>
    <link rel="stylesheet" href="css/multiselect.css">
    <link rel="stylesheet" href="css/vendorToSkuPriceMapping.css">
</head>

<div class="sku-mapping-container" data-ng-init="init()">
    <div class="page-header">
        <h1 class="page-title"><i class="material-icons">assignment</i> Vendor/Customer to SKU Mapping</h1>
    </div>

    <form id="basicSkuDetail" name="basicSkuDetailsForm" novalidate>
        <!-- Selection Panel - All fields in one compact component -->
        <div class="selection-panel">
            <div class="selection-panel-header">
                <i class="material-icons">settings</i>
                <h2>SKU Mapping Configuration</h2>
            </div>
            <div class="selection-panel-content">
                <!-- Vendor Selection Row -->
                <div class="form-row">
                    <div class="form-col" style="flex: 2;">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">business</i> Vendor/Customer*</label>
                            <select ui-select2 type="text" pattern=".*\S+.*" id="vendorNameId" class="form-control"
                                ng-model="selectedVendor"
                                data-ng-options="vendorName as (vendorName.name + ' [' + vendorName.category + ']') for vendorName in vendorNameList| orderBy : 'name' track by vendorName.id"
                                data-ng-change="showVendors(selectedVendor)" required>
                            </select>
                            <span class="focus-border"></span>
                            <p data-ng-show="basicSkuDetail.selectedVendor.$error.required" class="errorMessage">Please select vendor/customer.</p>
                        </div>
                    </div>
                    <div class="form-col" style="flex: 1;" data-ng-if="isContractInRunningState">
                        <div class="contract-toggle">
                            <label><i class="material-icons tiny">description</i> Create New Contract</label>
                            <div class="switch">
                                <label>
                                    No
                                    <input
                                        type="checkbox"
                                        id="createNewContract"
                                        ng-model="isNewContractByForce"
                                        ng-change="setNewContract(isNewContractByForce)"
                                        ng-disabled="workOrder.workOrderStatus == null || workOrder.workOrderStatus == 'PARTIALLY_REJECTED'">
                                    <span class="lever"></span>
                                    Yes
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Location Selection Row -->
                <div class="form-row" data-ng-if="isContractInRunningState">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">place</i> Vendor/Customer Location*</label>
                            <select ui-select2 type="text" pattern=".*\S+.*" id="dispatchLocationId" class="form-control"
                                ng-model="selectedDispatchLocation"
                                data-ng-options="d as (d.code + ' ,' + d.state.name) for d in vendorDispatchLocations| orderBy : 'code' track by d.code"
                                data-ng-change="setDispatchLocation(selectedDispatchLocation)"
                                required>
                            </select>
                            <span class="focus-border"></span>
                            <p data-ng-show="basicSkuDetail.selectedDispatchLocation.$error.required" class="errorMessage">Please select vendor/customer location.</p>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">location_city</i> Delivery Location*</label>
                            <div id="deliveryLocationId" style="text-align: left; position: relative;" ng-dropdown-multiselect=""
                                extra-settings="multiSelectSettings" options="allDeliveryLocationList"
                                selected-model="selectedLocation" translation-texts="{buttonDefaultText: 'Select Delivery Location'}">
                            </div>
                            <p data-ng-show="selectedLocation.length == 0" class="errorMessage">Please select at-least one location.</p>
                        </div>
                    </div>
                    <div class="form-col" data-ng-if="selectedLocation.length > 0 && listOfUnits.length > 0">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">domain</i> Delivery Unit*</label>
                            <div id="deliveryUnitId" style="text-align: left; position: relative;" ng-dropdown-multiselect=""
                                extra-settings="multiSelectSettings2" options="listOfUnits" selected-model="selectedUnit"
                                translation-texts="{buttonDefaultText: 'Select Delivery Unit'}">
                            </div>
                            <p data-ng-show="selectedUnit.length == 0" class="errorMessage">Please select at-least one unit.</p>
                        </div>
                    </div>
                </div>

                <!-- Product Selection Row -->
                <div class="form-row" data-ng-if="isContractInRunningState">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">category</i> Product Category*</label>
                            <select ui-select2 type="text" pattern=".*\S+.*" id="productCategoryId" class="form-control"
                                ng-model="selectedProductCategory" data-ng-change="showSkus(selectedProductCategory)"
                                data-ng-options="c as c.name for c in categories | orderBy:name track by c.name" required>
                            </select>
                            <span class="focus-border"></span>
                            <p data-ng-show="basicSkuDetail.selectedProductCategory.$error.required" class="errorMessage">Please select product category.</p>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">inventory_2</i> SKU*</label>
                            <select ui-select2 type="text" class="form-control" id="skuId" data-ng-model="selectedSku"
                                data-ng-change="showPackaging(selectedSku)"
                                data-ng-options="s as s.skuName for s in getSkus track by s.skuId" required>
                            </select>
                            <span class="focus-border"></span>
                            <p data-ng-show="basicSkuDetail.selectedSku.$error.required" class="errorMessage">Please select SKU.</p>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">inventory</i> Packaging*</label>
                            <select ui-select2 type="text" id="skuPackagingId" pattern=".*\S+.*" class="form-control"
                                ng-model="selectedPackaging"
                                data-ng-change="showSelectedPackaging(selectedPackaging)"
                                data-ng-options="p as p.packagingName for p in skuPkgs track by p.packagingId" required>
                            </select>
                            <span class="focus-border"></span>
                            <p data-ng-show="basicSkuDetail.selectedPackaging.$error.required" class="errorMessage">Please select packaging.</p>
                        </div>
                    </div>
                </div>

                <!-- Price and Add Button Row -->
                <div class="form-row" data-ng-if="isContractInRunningState">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">attach_money</i> Price*</label>
                            <input type="text" id="skuPriceId" pattern="^\d{1,10}(\.\d{1,3})?$" class="form-control"
                                ng-model="skuPrice" data-ng-change="showSelectedPrice(skuPrice)" required>
                            <span class="focus-border"></span>
                            <p data-ng-show="basicSkuDetail.skuPrice.$error.required" class="errorMessage">Please enter price.</p>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-primary"
                                data-ng-class="{'disabled':checkForValidStatus(basicSkuDetailsForm.$valid)}"
                                data-ng-click="addPricing()"
                                data-ng-disabled="checkForValidStatus(basicSkuDetailsForm.$valid)">
                                <i class="material-icons">add_circle</i> Add SKU
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Grid View -->
    <div class="grid-panel" id="gridView" data-ng-if="showGrid()">
        <div class="grid-panel-header">
            <i class="material-icons">list</i>
            <h2>SKU Mapping List</h2>
        </div>
        <div id="mappingsGrid" ui-grid="priceGridOptions" ui-grid-edit ui-grid-row-edit ui-grid-cellNav
            ui-grid-resize-columns ui-grid-move-columns class="grid" style="height: 400px;"></div>
    </div>

    <!-- Combined Approval and Contract Dates Panel -->
    <div class="combined-panel" data-ng-if="showApproverField || isEntryFoundForUpdation()">
        <div class="combined-panel-content">
            <!-- Approver Selection Section -->
            <div class="combined-panel-section" data-ng-if="showApproverField">
                <h3 class="combined-panel-section-title">Approval Request</h3>
                <div class="form-group">
                    <label class="form-label"><i class="material-icons tiny">verified_user</i> Request Price approval from*</label>
                    <select id="inputCreated2" ui-select2="selectEmployee" class="form-control"
                        data-ng-model="approvalFrom"
                        data-ng-change="setEmployee(approvalFrom)"
                        data-placeholder="Select Approver"
                        data-ng-options="s as (s.name + ' [' + s.id + ']') for s in searchEmpValues| orderBy : 'name' track by s.id">
                    </select>
                </div>
            </div>

            <!-- Contract Dates Section -->
            <div class="combined-panel-section" data-ng-if="isEntryFoundForUpdation()">
                <h3 class="combined-panel-section-title">Effective Dates</h3>
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">event</i> Start Date</label>
                            <input input-date type="text" name="created" id="inputCreated1" class="form-control"
                                ng-model="startDate" container="" format="yyyy-mm-dd"
                                data-ng-change="setStartDate(startDate)" />
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label class="form-label"><i class="material-icons tiny">event_busy</i> End Date <span style="color: red">{{ previousWOState(workOrder.workOrderStatus) && !isNewContractByForce ? ' [ Contract End Date : ' + (workOrder.endDate | date:'yyyy-MM-dd') + ' ]' : '' }}</span></label>
                            <input input-date type="text" name="created" id="inputCreated3" class="form-control"
                                ng-model="endDate" container="" format="yyyy-mm-dd"
                                data-ng-change="setEndDate(endDate)" data-ng-disabled="previousWOState(workOrder.workOrderStatus) && !isNewContractByForce"/>
                        </div>
                    </div>
                </div>

                <div class="btn-container">
                    <button class="btn btn-primary" ng-click="previewUpdationModel()" data-ng-if="dateNotNull()">
                        <i class="material-icons">visibility</i> Preview
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates for grid cells -->
<script type="text/ng-template" id="skuIdTemplate.html">
    <div class="ui-grid-cell-contents">
        <a style="cursor: pointer" data-ng-click="grid.appScope.showPreview($event, row.entity.sku.id,'SKU')">{{row.entity.sku.name}}</a>
    </div>
</script>

<script type="text/ng-template" id="statusBatch.html">
    <div class="ui-grid-cell-contents">
        <span class="badge badge-active" ng-if="row.entity.status === 'ACTIVE'">ACTIVE</span>
        <span class="badge badge-active" ng-if="row.entity.status === 'APPROVED'">APPROVED</span>
        <span class="badge badge-inactive" ng-if="row.entity.status === 'IN_ACTIVE'">IN-ACTIVE</span>
        <span class="badge badge-inactive" ng-if="row.entity.status === 'REJECTED'">REJECTED</span>
        <span class="badge badge-inactive" ng-if="row.entity.status === 'ARCHIVED'">ARCHIVED</span>
        <span class="badge badge-active" ng-if="row.entity.status === 'CREATED'">CREATED</span>
        <span class="badge badge-orange" ng-if="row.entity.status === 'ADDED'">ADDED</span>
        <span class="badge badge-inactive" ng-if="row.entity.status === 'REMOVED'">REMOVED</span>
    </div>
</script>

<script type="text/ng-template" id="statusChangeButton.html">
    <div class="ui-grid-cell-contents">
        <button class="btn btn-primary btn-xs-small"
                ng-click="grid.appScope.updateRow(row.entity, true)"
                data-ng-if="!row.entity.updated"
                data-ng-hide="row.entity.status == 'APPROVED' || row.entity.removed || row.entity.status == 'CREATED' ||
                            row.entity.status === 'IN_ACTIVE' || row.entity.status === 'REMOVED'">
            <span>Update</span>
        </button>
        <button class="btn btn-warning btn-xs-small"
                ng-click="grid.appScope.cancelRow(row.entity)"
                data-ng-if="row.entity.updated && !row.entity.removed">
            <span>Cancel Update</span>
        </button>
        <button class="btn btn-danger btn-xs-small"
                ng-click="grid.appScope.rejectRow(row.entity)"
                data-ng-if="!row.entity.removed"
                data-ng-hide="row.entity.updated || row.entity.status == 'CREATED' || row.entity.status === 'REMOVED' ||
                                row.entity.status === 'ACTIVE' || row.entity.status === 'IN_ACTIVE' || row.entity.status === 'APPROVED' || (!isNewContractByForce && row.entity.isStatusActive == 'Y')">
            <span>Remove</span>
        </button>
        <button class="btn btn-warning btn-xs-small"
                ng-click="grid.appScope.cancelRow(row.entity)"
                data-ng-if="row.entity.removed && !row.entity.updated"
                data-ng-hide="(!isNewContractByForce && row.entity.isStatusActive == 'Y')">
            <span>Cancel Remove</span>
        </button>
    </div>
</script>



<script type="text/ng-template" id="UnitsModel.html" class="modal-largee">
    <div data-ng-init="initUnitModel()">
        <div class="modal-content" style="overflow-x: auto; max-height: 300px;">
            <div class="row">
                <h5><b>Units of {{row.deliveryLocation}}</b></h5>
            </div>
            <div class="ui-grid-cell-contents" id="deliveryUnitId1" style="text-align: left; height: 300px;"
                 data-ng-show="!isViewOnly && filteredUnits.length > 0"
                 ng-dropdown-multiselect=""
                 extra-settings="multiSelectSettings2"
                 options="filteredUnits"
                 selected-model="selectedUnitForId">
            </div>
            <div class="ui-grid-cell-contents" style=" height: 300px;"
                 data-ng-show="isViewOnly && selectedUnitForId != null && selectedUnitForId.length > 0">
                <h5 style="font-size: 12px;" ng-repeat="unit in selectedUnitForId">{{unit.value}}</h5>
            </div>
            <div data-ng-show="!isViewOnly && filteredUnits.length == 0">
                <span style="color: red;">No units available for {{row.deliveryLocation}} to select.</span>
            </div>
            <div data-ng-show="isViewOnly && selectedUnitForId.length == 0">
                <span style="color: red;">No units selected for {{row.deliveryLocation}}.</span>
            </div>
        </div>
        <div class="modal-footer right">
            <button class="red waves-effect waves-green btn-flat" data-ng-click="close()">Close</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="vendorToSkuPriceMappingModal.html">
    <div id="previewExcessQuantity" data-ng-init="initSKUPriceUpdationModal()" class="modal-large">
        <div class="row">
            <h5>Vendor/Customer Price Update</h5>
        </div>
        <div class="row" id="gridView">
            <div class="col s12">
                <div id="mappingsGrid" ui-grid="showGrid" ui-grid-edit ui-grid-row-edit ui-grid-cellNav
                    ui-grid-resize-columns ui-grid-move-columns class="grid col s12"></div>
            </div>
        </div>
        <span style="color: red;">Request Price approval from : {{approvalFrom.name}} [{{approvalFrom.id}}]</span>
        <div class="modal-footer right" data-ng-show="showGrid.data != null && showGrid.data.length > 0">
            <input type="button"
                   ng-attr-value="{{(availableDocumentId === undefined || availableDocumentId === null) ? 'Upload File *' : 'Download File'}}"
                   data-ng-click="(availableDocumentId === undefined || availableDocumentId === null) ? uploadDoc() : downloadDoc()"
                   ng-class="(availableDocumentId === undefined || availableDocumentId === null) ? 'waves-green btn' : 'orange btn'"
                   style="font-weight: bold; color: white"/>
            <button class="waves-effect waves-red btn red" data-ng-click="close()">Cancel</button>
            <button class="waves-effect waves-green btn" data-ng-click="submit()">Submit</button>
        </div>
        <div data-ng-show="showGrid.data == null || showGrid.data.length == 0">
            <h5>Please add SKU's to continue!</h5>
            <button class="waves-effect waves-red btn red" data-ng-click="close()">Back</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="statusButton.html">
    <div class="ui-grid-cell-contents">
        <span class="span badge active" ng-if="row.entity.detail.newStatus === 'APPROVED'">APPROVED</span>
        <span class="span badge orange" ng-if="row.entity.detail.newStatus === 'CREATED'">CREATED</span>
        <span class="btn badge inactive" ng-if="row.entity.detail.newStatus === 'REMOVED'">REMOVED</span>
    </div>
</script>