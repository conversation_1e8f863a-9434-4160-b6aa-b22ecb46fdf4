<style>
    .highlight-row {
        background-color: #c1d2f5 !important;
    }
    .modal-large {
      width: 100% !important;
    }

    .modal-small {
      width: 30% !important;
    }

    .popeye-modal {
      width: 80% !important;
    }

</style>
<div class="row white z-depth-3" data-ng-init="initUnitToDerivedProductsMapping()">
    <div class="col s12">
        <h4>Unit to Derived Products Mapping</h4>
    </div>
    <div class="row" id="mappingDivDisplay">
        <div class="col s12">
            <div class="scm-form" style="padding: 10px 0;">

                <!-- Row 1: Mapping Type -->
                <div class="row">
                    <div class="col s6">
                        <label class="black-text">Mapping Type</label>
                        <select
                                ui-select2="mappingTypeSelection"
                                id="mappingTypeSelection"
                                name="mappingTypeSelection"
                                data-placeholder="Select mapping type"
                                data-ng-model="selectedMappingType"
                                data-ng-options="unitMappingName as unitMappingName.name for unitMappingName in mappingTypeList track by unitMappingName.id"
                                data-ng-change="selectMappingType(selectedMappingType)">
                        </select>
                    </div>
                </div>

                <!-- Row 2: Value Select & Clone From -->
                <div class="row">
                    <!-- Value Select -->
                    <div class="col s6">
                        <label class="black-text">
                            {{selectedMappingType.id == 1 ? 'Select Unit' : selectedMappingType.id == 2 ? 'Select Derived Product' : ''}}
                        </label>
                        <select
                                ui-select2="uiSelect1"
                                id="uiSelect1"
                                name="uiSelect1"
                                data-placeholder="Select value"
                                data-ng-model="valueSelected"
                                data-ng-change="onChangeValue(valueSelected)"
                                data-ng-options="value as value.name for value in valueDataList">
                        </select>
                    </div>

                    <!-- Clone From -->
                    <div class="col s6" data-ng-show="valueSelected != undefined && valueSelected != null">
                        <label class="black-text" for="cloneDisplayData">Clone From</label>
                        <select
                                style="width: 100%;"
                                ui-select2="cloneDisplayData"
                                id="cloneDisplayData"
                                name="cloneDisplayData"
                                data-ng-model="cloningMapping"
                                data-placeholder="Select clone value"
                                data-ng-options="clone as clone.name for clone in valueDataList | orderBy: 'name' | filter : filterSelectedValue track by clone.id">
                        </select>
                    </div>
                </div>

                <!-- Row 3: Search Button under Value Select -->
                <div class="row">
                    <div class="col s6">
                        <button
                                data-ng-click="searchMappingShow(valueSelected.id)"
                                data-tooltip="Search Mappings"
                                class="btn left"
                                tooltipped>
                            Search
                        </button>
                    </div>

                    <!-- Clone Button under Clone Select -->
                    <div class="col s6" data-ng-show="valueSelected != undefined && valueSelected != null">
                        <button
                                data-ng-click="makeClone()"
                                data-tooltip="Clone Mappings from the selected Unit"
                                class="btn right"
                                tooltipped
                                acl-action="SMMUSUP">
                            Clone
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <div class="row" id="mappingDivSubmit" data-ng-if="productsGrid.data.length > 0">
        <div class="col s10">
            <span>Mappings for {{selectedMappingType.name}}: <b>{{showName}}</b></span>
        </div>
        <div class="col s12">
            <button
                    data-ng-click="verifyAndSubmit()"
                    data-tooltip="Submit Changes"
                    class="btn right" tooltipped>Submit</button>
        </div>

    <div class="row" id="gridView" data-ng-if="productsGrid.data.length > 0">
        <div class="col s12">
            <div
                    id="mappingsGrid"
                    ui-grid="productsGrid"
                    ui-grid-edit
                    ui-grid-row-edit
                    ui-grid-cellNav
                    ui-grid-resize-columns
                    ui-grid-move-columns
                    ui-grid-pagination
                    class="grid col s12"
                    style="height: 700px;">
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="cloneDisplayModal.html">
    <div id="cloneDisplayModal" data-ng-init="initCloneDisplayModal()" class="modal-large">
        <div class="row">
            <h5>{{mappingType.name}} Clone from <b>{{cloningMapping.name}}</b> to <b>{{valueSelected.name}}</b></h5>
            <div class="col s12">
                <table class="striped highlight">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>To FulfillmentType</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="mapping in paginatedList">
                            <td>{{mappingType.id == 1 ? mapping.productId : mapping.unitId}}</td>
                            <td>{{mappingType.id == 1 ? mapping.productName : mapping.unitName}}</td>
                            <td>{{mapping.fulfillmentType}}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="col s12" style="margin-top: 10px;">
                    <ul class="pagination center-align">
                        <li class="{{currentPage === 1 ? 'disabled' : 'waves-effect'}}">
                            <a href="" data-ng-click="currentPage > 1 && (currentPage = currentPage - 1) && updatePagination()">
                                <i class="material-icons">chevron_left</i>
                            </a>
                        </li>
                        <li class="active"><a href="">{{currentPage}}</a></li>
                        <li class="{{(currentPage * pageSize) >= cloningMappingList.length ? 'disabled' : 'waves-effect'}}">
                            <a href="" data-ng-click="(currentPage * pageSize) < cloningMappingList.length && (currentPage = currentPage + 1) && updatePagination()">
                                <i class="material-icons">chevron_right</i>
                            </a>
                        </li>
                        <p class="center-align grey-text">
                            Total: {{cloningMappingList.length}} entries
                        </p>
                    </ul>
                </div>

            </div>
            <div class="col s12" style="margin-top: 20px;">
                <button
                        data-ng-click="submitClone()"
                        class="btn right">Submit</button>
            </div>
        </div>
    </div>
</script>