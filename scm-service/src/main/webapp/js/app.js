/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


var scmApp = angular.module("scmApp", ['ui.router','ngSanitize',
    'ui.materialize', 'ui.select2', 'dndLists', 'ngCookies','AngularPrint','fsm',
    'ngAnimate', 'ngTouch', 'ui.grid', 'ui.grid.saveState', 'ui.grid.selection', 'ui.grid.edit','ui.grid.exporter',
    'ui.grid.cellNav', 'ui.grid.resizeColumns', 'ui.grid.moveColumns', 'ui.grid.pinning', 'ui.grid.grouping','pathgather.popeye'
    ,'angularjs-dropdown-multiselect','ui.grid.pagination','ui.grid.expandable','highcharts-ng']);

//scmApp.value('version', '9.9.7');
scmApp.config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {
    $urlRouterProvider.otherwise("/login");

    $stateProvider.state('signup', {
        url: "/signup",
        templateUrl: window.version+"views/signup.html",
        controller: function ($scope) {
            $scope.tagline = "Signup";
        }
    }).state('login', {
        url: "/login",
        templateUrl: window.version+'views/login.html',
        params: {accessDenied: false},
        controller: 'loginCtrl'
    }).state('metadata',{
        url: "/metadata",
        templateUrl: window.version+'views/metadataLoader.html',
        controller: 'metadataCtrl'
    }).state('menu', {
        url: "/menu",
        templateUrl: window.version+'views/menu.html',
        params: {permissions: null},
        controller: 'menuCtrl'
    }).state('dayCloseShutdown',{
        url:"/shutdown",
        templateUrl:window.version+"views/shutdown.html",
        controller:'shutDownCtrl'
    }).state('menu.productList', {
        url: "/products",
        templateUrl: window.version+'views/productList.html',
        controller: 'productListCtrl',
    }).state('menu.productDashboard', {
        templateUrl: window.version+'views/productDashboard.html',
        controller: 'productDashboardCtrl',
    }).state('menu.skuDashboard', {
        templateUrl: window.version+'views/skuDashboard.html',
        controller: 'skuDashboardCtrl',
    }).state('menu.assetList', {
        url: "/assets",
        templateUrl: window.version+'views/assetList.html',
        controller: 'assetListCtrl',
        params: {assets: null, permissions: null}
    }).state('menu.assetLostConfirmation', {
        url: "/assetsLost",
        templateUrl: window.version+'views/assetLostConfirmation.html',
        controller: 'assetLostConfirmationCtrl',
    }).state('menu.assetRecoveryList', {
        url: "/assetsRecovery",
        templateUrl: window.version+'views/assetRecoveryList.html',
        controller: 'assetRecoveryListCtrl'
    }).state('menu.assetInventoryList', {
        url: "/assetsInventory",
        templateUrl: window.version+'views/assetInventoryList.html',
        controller: 'assetInventoryListCtrl',
        params: {assets: null, permissions: null}
    }).state('menu.assetTransfers', {
        url: "/assetTransfers",
        templateUrl: window.version+'views/assetTransfers.html',
        controller: 'AssetTransfersCtrl'
    }).state('menu.fixedAssetRecovery', {
        url: "/fixedAssetRecovery",
        templateUrl: window.version+'views/fixedAssetRecovery.html',
        controller: 'FixedAssetRecoveryCtrl'
    }).state('menu.addProduct', {
        url: "/product/add",
        templateUrl: window.version+'views/newProduct.html',
        params: {productDef: null, empType : null},
        controller: 'productCtrl'
    }).state('menu.addAttribute', {
        url: "/attribute/add",
        templateUrl: window.version+'views/addAttribute.html',
        params: {attributeDef: null},
        controller: 'addAttributeCtrl'
    }).state('menu.stockTake', {
        url: "/update/stocktake",
        templateUrl: window.version+'views/stockTake.html',
        params: {attributeDef: null},
        controller: 'stockTakeCtrl'
    }).state('menu.addProfile', {
        url: "/profile/add",
        templateUrl: window.version+'views/newProfile.html',
        params: {profileDef: null},
        controller: 'profileCtrl'
    }).state('menu.addSKU', {
        url: "/product/sku/add",
        templateUrl: window.version+'views/newSKU.html',
        controller: 'skuCtrl',
        params: {skuDef: null, empType : null}
    }).state('menu.skuList', {
        url: "/product/skus",
        templateUrl: window.version+'views/skuList.html',
        controller: 'skuListCtrl'
    }).state('menu.attrList', {
        url: '/attributes',
        templateUrl: window.version+'views/attributes.html',
        controller: 'attributeCtrl'
    }).state('menu.attrValues', {
        url: '/attributeValues',
        templateUrl: window.version+'views/addAttrValues.html',
        controller: 'attrValueMappingCtrl'
    }).state('menu.attrMappings', {
        url: '/attributeMappings',
        templateUrl: window.version+'views/attributeMappings.html',
        controller: 'attrMappingListingCtrl'
    }).state('menu.attrProductMappings', {
        url: '/attrProductMappings',
        templateUrl: window.version+'views/attrProductMappings.html',
        controller: 'attrProductMappingsCtrl'
    }).state('menu.packaging', {
        url: '/packaging',
        templateUrl: window.version+'views/packaging.html',
        controller: 'packagingCtrl'
    }).state('menu.mapPackaging', {
        url: '/mapPackaging',
        templateUrl: window.version+'views/mapPackaging.html',
        controller: 'mapPackagingProductCtrl'
    }).state('menu.mapSkuPackaging', {
        url: '/mapSkuPackaging',
        templateUrl: window.version+'views/mapSkuPackaging.html',
        controller: 'mapSkuPackagingCtrl'
    }).state('menu.mapAttrValues', {
        url: '/mapAttrValues',
        templateUrl: window.version+'views/mapAttrValues.html',
        controller: 'mapAttrValueCtrl'
    }).state('menu.refOrderCreate', {
        url: '/refOrderCreate',
        templateUrl: window.version+'views/refOrderCreate.html',
        controller: 'refOrderCreateCtrl'
    }).state('menu.adhocOrderCreate', {
        url: '/adhocOrderCreate',
        templateUrl: window.version+'views/adhocOrderCreate.html',
        controller: 'adhocOrderCreateCtrl',
        params: {assetOrder: false, clonedItems:null, fulfilmentUnit:null}
    }).state('menu.refOrderFind', {
        url: '/refOrderFind',
        templateUrl: window.version+'views/refOrderFind.html',
        controller: 'refOrderFindCtrl'
    }).state('menu.refOrderAction', {
        url: '/refOrderAction',
        templateUrl: window.version+'views/refOrderAction.html',
        controller: 'refOrderActionCtrl'
    }).state('menu.updateInventory', {
        url: '/inventory',
        templateUrl: window.version+'views/inventory.html',
        controller: 'inventoryCtrl'
    }).state('menu.varianceEdit', {
        url: '/varianceEdit',
        templateUrl: window.version+'views/varianceEdit.html',
        controller: 'varianceEditCtrl'
    }).state('menu.currentInventory', {
        url: '/current-inventory',
        templateUrl: window.version+'views/currentInventory.html',
        controller: 'currentInventoryCtrl'
    }).state('menu.currentPrice', {
        url: '/current-price',
        templateUrl: window.version+'views/currentPrice.html',
        controller: 'currentPriceCtrl'
    }).state('menu.trEpCreate', {
        url: '/trEpCreate',
        templateUrl: window.version+'views/trEpCreate.html',
        controller: 'trEpCreateCtrl'
    }).state('menu.trOrderCreate', {
        url: '/trOrderCreate',
        templateUrl: window.version+'views/trOrderCreate.html',
        controller: 'trOrderCreateCtrl'
    }).state('menu.trOrderCreateBulk', {
        url: '/trOrderCreateBulk',
        templateUrl: window.version+'views/trOrderCreateBulk.html',
        controller: 'trOrderCreateBulkCtrl'
    }).state('menu.grActivity', {
        url: '/grActivity',
        templateUrl: window.version+'views/grActivity.html',
        controller: 'grActivityCtrl'
    }).state('menu.milkBreadBypass', {
        url: '/milkBreadBypass',
        templateUrl: window.version+'views/milkBreadBypass.html',
        controller: 'milkBreadBypassCtrl'
    }).state('menu.whWastage',{
        url:"/whWastage",
        templateUrl:"views/whWastageView.html",
        controller: 'wastageWHCtrl'
    }).state('menu.wastage',{
        url:"/wastage",
        templateUrl:"views/wastageView.html",
        controller: 'wastageCtrl'
    }).state('menu.reqOrderMgt',{
        url:"/reqOrderMgt",
        templateUrl:"views/reqOrderMgt.html",
        controller: 'reqOrderMgtCtrl'
    }).state('menu.reqOrderAction',{
        url:"/reqOrderAction",
        templateUrl:"views/reqOrderAction.html",
        controller: 'reqOrderActionCtrl'
    }).state('menu.specialOrderCreate',{
        url:"/specialOrderCreate",
        templateUrl:"views/specialOrderCreate.html",
        controller: 'specialOrderCreateCtrl'
    }).state('menu.orderingSchedule',{
        url:"/orderingSchedule",
        templateUrl:"views/orderingSchedule.html",
        controller: 'orderingScheduleCtrl'
    }).state('menu.allOrderingSchedules',{
        url:"/allOrderingSchedules",
        templateUrl:"views/allOrderingSchedules.html",
        controller: 'allOrderingSchedulesCtrl'
    }).state('menu.acknowledgeRO',{
        url:"/acknowledgeOrders",
        templateUrl: window.version+'views/acknowledgeRO.html',
        controller: 'acknowledgeOrderCtrl'
    }).state('menu.trOrderMgt',{
        url:"/trOrderMgt",
        templateUrl: window.version+'views/trOrderMgt.html',
        controller: 'trOrderMgtCtrl'
    }).state('menu.BulkTrOrderMgt',{
        url:"/BulkTrOrderMgt",
        templateUrl: window.version+'views/BulkTrOrderMgt.html',
        controller: 'BulkTrOrderMgtCtrl'
    }).state('menu.trOrderAction',{
        url:"/trOrderAction",
        templateUrl: window.version+'views/trOrderAction.html',
        controller: 'trOrderActionCtrl'
    }).state('menu.grOrderMgt',{
        url:"/grOrderMgt",
        templateUrl: window.version+'views/grOrderMgt.html',
        controller: 'grOrderMgtCtrl'
    }).state('menu.grOrderAction',{
        url:"/grOrderAction",
        templateUrl: window.version+'views/grOrderAction.html',
        controller: 'grOrderActionCtrl'
    }).state('menu.standaloneTO',{
        url:"/standaloneTO",
        templateUrl: window.version+'views/standaloneTO.html',
        controller: 'standaloneTOCtrl'
    }).state('menu.standaloneTONew',{
        url:"/standaloneAssetTONew",
        templateUrl: window.version+'views/standaloneAssetTONew.html',
        controller: 'standaloneAssetTONewCtrl'
    })
    // .state('menu.standaloneAssetTO',{
    //     url:"/standaloneAssetTO",
    //     templateUrl: window.version+'views/standaloneAssetTO.html',
    //     controller: 'standaloneAssetTOCtrl'
    // })
    .state('menu.specializedOrderReport',{
        url:"/specializedOrderReport",
        templateUrl: window.version+'views/specializedOrderReport.html',
        controller: 'specializedOrderReportCtrl'
    }).state('menu.CustomSumoReports',{
        url:"/CustomSumoReports",
        templateUrl: window.version+'views/CustomSumoReports.html',
        controller: 'customSumoReportsCtrl'
    }).state('menu.vendorManagement',{
        url:"/vendorManagement",
        templateUrl: window.version+'views/vendorManagement.html',
        controller: 'vendorManagementCtrl'
    }).state('menu.skuPriceUpdate',{
        url:"/skuPriceUpdate",
        templateUrl: window.version+'views/skuPriceUpdate.html',
        controller: 'SKUPriceUpdateCtrl'
    }).state('menu.skuPriceHistory',{
        url:"/skuPriceHistory",
        templateUrl: window.version+'views/skuPriceHistory.html',
        controller: 'SKUPriceHistoryCtrl'
    }).state('menu.lostTag',{
        url:"/lostTag",
        templateUrl: window.version+'views/lostTag.html',
        controller: 'LostTagCtrl'
    }).state('menu.lostAsset',{
        url:"/lostAsset",
        templateUrl: window.version+'views/lostAsset.html',
        controller: 'LostAssetCtrl'
    }).state('menu.unitVendorMapping',{
        url:"/unitVendorMapping",
        templateUrl: window.version+'views/unitVendorMapping.html',
        controller: 'unitVendorMappingCtrl'
    }).state('menu.vendorRequest',{
        url:"/vendorRequest",
        params:{isOnlyForNewRegistration:false},
        templateUrl: window.version+'views/vendorRequest.html',
        controller: 'vendorRequestCtrl'
    }).state('menu.businessToCustomerMapping',{
        url:"/businessToCustomerMapping",
        templateUrl: window.version+'views/businessToCustomerMapping.html',
        controller: 'businessToCustomerCtrl'
    }).state('menu.unitToSkuMapping',{
        url:"/unitToSkuMapping",
        templateUrl: window.version+'views/unitToSkuMapping.html',
        params:{isCafe:false},
        controller: 'unitToSkuMappingCtrl'
    }).state('menu.unitToDerivedProductsMapping',{
       url:"/unitToDerivedProductsMapping",
       templateUrl: window.version+'views/unitToDerivedProductsMapping.html',
       controller: 'unitToDerivedProductsMappingCtrl'
    }).state('menu.vendorToSkuMapping',{
        url:"/vendorToSkuMapping",
        templateUrl: window.version+'views/vendorToSkuMapping.html',
        controller: 'vendorToSkuMappingCtrl'
    }).state('menu.unitToProductProfileMapping',{
        url: "/unitToProductProfileMapping",
        templateUrl: window.version +'views/unitToProductProfileMapping.html',
        controller: "unitToProductProfileMappingCtrl"
    }).state('menu.vendorToUnitToSkuMapping',{
        url:"/vendorToUnitToSkuMapping",
        templateUrl: window.version+'views/vendorToUnitToSkuMapping.html',
        controller: 'vendorToUnitToSkuMappingCtrl'
    }).state('menu.vendorToSkuPriceUpdate',{
        url:"/vendorToSkuPriceUpdate",
        templateUrl: window.version+'views/vendorToSkuPriceUpdate.html',
        controller: 'vendorToSkuPriceUpdateCtrl'
    }).state('menu.unitDistanceMapping',{
        url:"/unitDistanceMapping",
        templateUrl: window.version+'views/unitDistanceMapping.html',
        controller: 'unitDistanceMappingCtrl'
    }).state('menu.skuPackagingTaxMapping',{
        url:"/skuPackagingTaxMapping",
        templateUrl: window.version+'views/skuPackagingTaxMapping.html',
        controller: 'skuPackagingTaxMappingCtrl'
    }).state('menu.viewVendor',{
        url:"/viewVendor",
        templateUrl: window.version+'views/vendors/vendorOverview.html',
        params:{edit:false,vendor:null,reqId:null,mode:null,data:null},
        controller: 'vendorEditCtrl'
    }).state('menu.supportLink',{
        url:"/supportLink",
        templateUrl: window.version+'views/supportLink.html',
        controller: 'supportLinkCtrl'
    }).state('menu.approveInvoice',{
        url:"/approveInvoice",
        templateUrl: window.version+'views/approveInvoice.html',
        params:{createdInvoice:null, viewInvoice: false,raiseCreditNote :false},
        controller: 'approveInvoiceCtrl'
    }).state('menu.vendorInvoiceCreate',{
        url:"/createInvoice",
        templateUrl: window.version+'views/createInvoice.html',
        params : { returnInvoice: false},
        controller: 'invoiceCreateCtrl'
    }).state('menu.approvePo',{
        url:"/approvePo",
        templateUrl: window.version+'views/approvePo.html',
        params:{viewPO: false,createdPO:null},
        controller: 'approvePoCtrl'
    }).state('menu.vendorOrderCreate',{
        url:"/createPO",
        templateUrl: window.version+'views/createPO.html',
        controller: 'poCreateCtrl'
    }).state('menu.approveRegularVendorGR', {
        url: "/approveRegularVendorGR",
        templateUrl: window.version + 'views/approveRegularVendorGR.html',
        controller: 'approveRegularVendorGRCtrl',
        params:{vendor:null,dispatchLocation:null,grId:null}
    }).state('menu.viewVendorGR',{
        url:"/viewVendorGR",
        templateUrl: window.version+'views/viewVendorGR.html',
        controller: 'viewVendorGRCtrl',
        params:{vendor:null,dispatchLocation:null,grId:null}
    }).state('menu.viewRejectedVendorGR',{
        url:"/viewRejectedVendorGR",
        templateUrl: window.version+'views/viewRejectedVendorGR.html',
        controller: 'viewRejectedVendorGRCtrl',
        params:{vendor:null,dispatchLocation:null,grId:null}
    }).state('menu.vendorGrCreate',{
        url:"/createGR",
        templateUrl: window.version+'views/vendorGR.html',
        controller: 'vendorGrCreateCtrl'
    }).state('menu.viewVendorPRtoGR',{
        url:"/viewVendorPRtoGR",
        templateUrl: window.version+'views/viewVendorPrtoGR.html',
        controller: 'viewVendorPRtoGRCtrl',
    }).state('menu.prodPlanning',{
        url:"/prodPlanning",
        templateUrl: window.version+'views/prodPlanning.html',
        controller: 'prodPlanningCtrl'
    }).state('menu.prodHistory',{
        url:"/prodHistory",
        templateUrl: window.version+'views/prodHistory.html',
        controller: 'prodHistoryCtrl'
    }).state('menu.prodBooking',{
        url:"/prodBooking",
        templateUrl: window.version+'views/prodBooking.html',
        controller: 'prodBookingCtrl'
    }).state('menu.bookingHistory',{
        url:"/bookingHistory",
        templateUrl: window.version+'views/bookingHistory.html',
        controller: 'bookingHistoryCtrl'
    }).state('menu.dayClose',{
        url:"/dayClose",
        templateUrl: window.version+'views/dayClose.html',
        controller: 'dayCloseCtrl'
    }).state('menu.createPaymentRequest',{
        url:"/createPaymentRequest",
        templateUrl: window.version+'views/createPaymentRequest.html',
        controller: 'createPaymentRequestCtrl'
    }).state('menu.searchPaymentRequest',{
        url:"/searchPaymentRequest",
        templateUrl: window.version+'views/searchPaymentRequest.html',
        controller: 'searchPaymentRequestCtrl'
    }).state('menu.processPaymentRequest',{
        url:"/processPaymentRequest",
        templateUrl: window.version+'views/processPaymentRequest.html',
        controller: 'processPaymentRequestCtrl'
    }).state('menu.vendorAdvancePayment',{
        url:"/vendorAdvancePayment",
        templateUrl: window.version+'views/vendorAdvancePayment.html',
        controller: 'vendorAdvancePaymentCtrl'
    }).state('menu.searchDebitNote',{
        url:"/searchDebitNote",
        templateUrl: window.version+'views/searchDebitNote.html',
        controller: 'searchDebitNoteCtrl'
    }).state('menu.settlePaymentRequests',{
        url:"/settlePaymentRequests",
        templateUrl: window.version+'views/settlePaymentRequests.html',
        controller: 'settlePaymentRequestsCtrl'
    }).state('menu.rejectPaymentRequests',{
        url:"/rejectPaymentRequests",
        templateUrl: window.version+'views/rejectPaymentRequests.html',
        controller: 'rejectPaymentRequestsCtrl'
    }).state('menu.holidayCalendar',{
        url:"/holidayCalendar",
        templateUrl: window.version+'views/holidayCalendar.html',
        controller: 'holidayCalendarCtrl'
    }).state('menu.productProjections',{
        url:"/productProjections",
        templateUrl: window.version+'views/productProjections.html',
        controller: 'productProjectionsCtrl'
    }).state('menu.uploadUnitsProjections',{
        url:"/uploadUnitsProjections",
        templateUrl: window.version+'views/uploadUnitsProjections.html',
        controller: 'uploadUnitsProjectionsCtrl'
    }).state('menu.manageVendorDebitBalance',{
        url:"/manageVendorDebitBalance",
        templateUrl: window.version+'views/manageVendorDebitBalance.html',
        controller: 'manageVendorDebitBalanceCtrl'
    }).state('menu.sendTDSMailToVendor',{
        url:"/sendTDSMailToVendor",
        templateUrl: window.version+'views/sendTDSMailToVendor.html',
        controller: 'sendTDSMailToVendorCtrl'
    }).state('menu.createDispatch',{
        url:"/createDispatch",
        templateUrl: window.version+'views/createDispatch.html',
        controller: 'createDispatchCtrl'
    }).state('menu.searchDispatch',{
        url:"/searchDispatch",
        templateUrl: window.version+'views/searchDispatch.html',
        controller: 'searchDispatchCtrl'
    }).state('menu.vehicleMaster',{
        url:"/vehicleMaster",
        templateUrl: window.version+'views/vehicleMaster.html',
        controller: 'vehicleMasterCtrl'
    }).state('menu.createGatepass',{
        url:"/createGatepass",
        templateUrl: window.version+'views/createGatepass.html',
        controller: 'createGatepassCtrl'
    }).state('menu.searchGatepass',{
        url:"/searchGatepass",
        templateUrl: window.version+'views/searchGatepass.html',
        controller: 'searchGatepassCtrl'
    }).state('menu.gatepassVendorMapping',{
        url:"/gatepassVendorMapping",
        templateUrl: window.version+'views/gatepassVendorMapping.html',
        controller: 'gatepassVendorMappingCtrl'
    }).state('menu.createSO',{
        url:"/createServiceOrder",
        templateUrl: window.version+'views/createSO.html',
        controller: 'serviceOrderCtrl'
    }).state('menu.createNewSO',{
        url:"/createNewServiceOrder",
        templateUrl: window.version+'views/createNewSO.html',
        controller: 'serviceOrderNewCtrl'
    }).state('menu.viewSO',{
        url:"/viewServiceOrder",
        templateUrl: window.version+'views/viewSO.html',
        params:{createdSO:null,viewSO:true},
        controller: 'viewServiceOrderCtrl'
    }).state('menu.approveSO',{
        url:"/approveServiceOrder",
        templateUrl: window.version+'views/viewSO.html',
        params:{createdSO:null,viewSO:false},
        controller: 'viewServiceOrderCtrl'
    })
    .state('menu.createSR', {
        url:"/createServiceReceiving",
        templateUrl: window.version+'views/createSR.html',
        controller: 'createServiceRcvCtrl'
    }).state('menu.viewSR', {
        url:"/viewServiceReceiving",
        templateUrl: window.version+'views/viewSR.html',
        controller: 'viewSRCtrl'
    }).state('menu.viewSoToSr', {
        url:"/viewSoToSr",
        templateUrl: window.version+'views/viewSoToSr.html',
        controller: 'viewSoToSrCtrl'
    }).state('menu.createServicePR', {
        url:"/createServicePR",
        templateUrl: window.version+'views/createServicePR.html',
        controller: 'createServicePRCtrl'
    }).state('menu.createClassification', {
        url:"/createClassification",
        templateUrl: window.version+'views/createClassification.html',
        controller: 'createClassificationCtrl'
    }).state('menu.subCreateClassification', {
        url:"/createSubClassification",
        templateUrl: window.version+'views/createSubClassification.html',
        controller: 'createSubClassificationCtrl'
    }).state('menu.subSubCreateClassification', {
        url:"/createSubSubClassification",
        templateUrl: window.version+'views/createSubSubClassification.html',
        controller: 'createSubSubClassificationCtrl'
    }).state('menu.createCostCenter', {
        url:"/createCostCenter",
        templateUrl: window.version+'views/costCenter.html',
        controller: 'createCostCenterCtrl'
    }).state('menu.createCostElement', {
        url:"/createCostElement",
        templateUrl: window.version+'views/createCostElement.html',
        controller: 'createCostElement'
    }).state('menu.searchCostElement', {
        url:"/searchCostElement",
        templateUrl: window.version+'views/costElement.html',
        controller: 'searchCostElementCtrl'
    }).state('menu.costCenterToUserMap', {
        url:"/costCenterToUserMap",
        templateUrl: window.version+'views/costCenterToUserMap.html',
        controller: 'costCenterToUserMapCtrl'
    }).state('menu.employeeBccMapping', {
        url:"/employeeBccMapping",
        templateUrl: window.version+'views/employeeBccMapping.html',
        controller: 'employeeBccMappingCtrl'
    }).state('menu.costElementToVendorMap', {
        url:"/costElementToVendorMap",
        templateUrl: window.version+'views/costElementToVendorMap.html',
        controller: 'costElementToVendorMapCtrl'
    }).state('menu.vendorToCostElementMap', {
        url:"/vendorToCostElementMap",
        templateUrl: window.version+'views/vendorToCostElementMap.html',
        controller: 'vendorToCostElementMapCtrl'
    }).state('menu.costElementToCostCentreMap', {
        url:"/costElementToCostCentreMap",
        templateUrl: window.version+'views/costElementToCostCentreMap.html',
        controller: 'costElementToCostCentreMapCtrl'
    }).state('menu.costCenterToCostElementMap', {
        url:"/costCenterToCostElementMap",
        templateUrl: window.version+'views/costCenterToCostElementMap.html',
        controller: 'costCenterToCostElementMapCtrl'
    }).state('menu.vendorToCostCentreToCostElementMap', {
        url:"/vendorToCostCentreToCostElementMap",
        templateUrl: window.version+'views/vendorToCostCentreToCostElementMap.html',
        controller: 'vendorToCostCentreToCostElementMapCtrl'
    }).state('menu.productPackaging', {
        url:"/productPackaging",
        templateUrl: window.version+'views/productPackaging.html',
        controller: 'productPackagingCtrl'
    }).state('menu.regionProductPackaging', {
        url:"/regionProductPackaging",
        templateUrl: window.version+'views/regionProductPackaging.html',
        controller: 'regionProductPackagingCtrl'
    }).state('menu.uploadCapex', {
        url:"/uploadCapex",
        templateUrl: window.version+'views/uploadCapex.html',
        controller: 'uploadCapexCtrl'
    }).state('menu.viewCapexPoSo', {
        url:"/viewCapexPoSo",
        templateUrl: window.version+'views/viewCapexPoSo.html',
        controller: 'viewCapexPoSoCtrl',
        params: {capexId : null}
    }).state('menu.viewRecipe',{
        url:"/viewRecipe",
        templateUrl: window.version+'views/viewRecipe.html',
        controller: 'viewRecipeCtrl'
    }).state('menu.refOrderCreateV1',{
        url:"/refOrderCreateV1",
        templateUrl: window.version+'views/refOrderCreateV1.html',
        params:{orderingEvents:[]},
        controller: 'refOrderCreateCtrlV1'
    }).state('menu.suggestiveOrdering',{
        url:"/suggestiveOrdering",
        templateUrl: window.version+'views/suggestiveOrdering.html',
        params:{orderingEvents:[]},
        controller: 'suggestiveOrderingController'
    }).state('menu.refOrderCreateV2',{
        url:"/refOrderCreateV2",
        templateUrl: window.version+'views/refOrderCreateV2.html',
        controller: 'refOrderCreateCtrlV2'
    }).state('menu.bulkStandaloneTO',{
        url:"/bulkStandaloneTO",
        templateUrl: window.version+'views/BulkStandaloneTO.html',
        controller: 'BulkStandaloneTOCtrl'
    }).state('menu.monkIngredientsConverter',{
        url:"/monkIngredientsConverter",
        templateUrl: window.version+'views/MonkIngredientsConverter.html',
        controller: 'monkIngredientsConverterCtrl'
    }).state('menu.productShelfLife',{
        url:"/productShelfLife",
        templateUrl: window.version+'views/ProductShelfLife.html',
        controller: 'ProductShelfLifeCtrl'
    }).state('menu.specializedOrderInvoice',{
        url:"/specializedOrderInvoice",
        templateUrl: window.version+'views/specializedOrderInvoice.html',
        controller: 'specializedOrderInvoiceCtrl'
    }).state('menu.assetConvertor',{
        url:"/assetConvertor",
        templateUrl: window.version+'views/assetConvertor.html',
        controller: 'assetConvertorCtrl'
    }).state('menu.addMasterDocument',{
        url:"/addMasterDocument",
        templateUrl: window.version+'views/addMasterDocument.html',
        controller: 'masterDocumentCtrl'
    }).state('menu.costElementToDocumentMap',{
        url:"/costElementToDocumentMap",
        templateUrl: window.version+'views/costElementToDocumentMap.html',
        controller: 'costElementToDocumentMapCtrl'
    }).state('menu.nonScannableMapping',{
        url:"/nonScannableMapping",
        templateUrl: window.version+'views/nonScannableAssetMapping.html',
        controller: 'nonScannableAssetMappingCtrl'
    }).state('menu.trGrOrderCreate', {
        url: '/trGrOrderCreate',
        templateUrl: window.version + 'views/trGrOrderCreate.html',
        controller: 'trGrOrderCreateCtrl'
    }).state('menu.outWardRegister',{
        url:"/outWardRegister",
        templateUrl: window.version+'views/outWardRegister.html',
        controller:'outWardRegisterCtrl'
    }).state('menu.reverseTO',{
        url:"/reverseTO",
        templateUrl: window.version+'views/reverseTO.html',
        controller: 'reverseTOCtrl'
    }).state('menu.b2bOutWardRegister',{
        url:"/b2bOutWardRegister",
        templateUrl: window.version+'views/outWardRegister.html',
        controller:'outWardRegisterCtrl',
        params : {
            type : "B2B_SALES"
        }
    }).state('menu.ecomOutWardRegister',{
        url:"/ecomOutWardRegister",
        templateUrl: window.version+'views/outWardRegister.html',
        controller:'outWardRegisterCtrl',
        params : {
            type : "ECOM"
        }
    }).state('menu.acknowledgeVariance', {
        url: '/acknowledgeVariance',
        templateUrl: window.version+'views/acknowledgeVariance.html',
        controller: 'acknowledgeVarianceCtrl'
    }).state('menu.viewCreditNote',{
        url:"/viewCreditNote",
        templateUrl: window.version+'views/viewCreditNote.html',
        controller: 'viewCreditNoteCtrl'
    }).state('menu.vendorToSkuPriceMapping', {
         url: '/vendorToSkuPriceMapping',
         templateUrl: window.version+'views/vendorToSkuPriceMapping.html',
         controller: 'vendorToSkuPriceMappingCtrl'
    }).state('menu.vendorToSkuPriceApproval', {
         url: '/vendorToSkuPriceApproval',
         templateUrl: window.version+'views/vendorToSkuPriceApproval.html',
         controller: 'vendorToSkuPriceApprovalCtrl'
    }).state('menu.vendorToSkuPrice', {
        url: '/vendorToSkuPrice',
        templateUrl: window.version+'views/vendorToSkuPrice.html',
        controller: 'vendorToSkuPriceCtrl'
    }).state('menu.vendorToSkuRequest', {
        url: '/vendorToSkuRequest',
        templateUrl: window.version+'views/vendorToSkuRequest.html',
        controller: 'vendorToSkuRequestCtrl'
    }).state('menu.vendorToSkuPreview', {
        url: '/vendorToSkuPreview',
        templateUrl: window.version+'views/vendorToSkuPreview.html',
        controller: 'vendorToSkuPreviewCtrl'
    }).state('menu.vendorContract', {
        url: '/vendorContract',
        templateUrl: window.version+'views/vendorContract.html',
        controller: 'vendorContractCtrl'
    }).state('menu.foundAsset',{
        url:"/foundAsset",
        templateUrl: window.version+'views/foundAsset.html',
        controller: 'FoundAssetController'
    }).state('menu.ldcVendor',{
        url:"/ldcVendor",
        templateUrl: window.version+'views/ldcVendor.html',
        controller: 'LdcVendorController'
    });
}]).service('$toastService', ['$rootScope', '$window', function ($rootScope, $window) {
    var service = this;
    var defaultDuration = 4000;

    service.create = function (message, callback) {
        if ($window.Materialize) {
            Materialize.toast(message, defaultDuration);
            if(callback!=undefined){
                callback();
            }
        }
    };
}]).service('$alertService', ['$rootScope', '$window', function ($rootScope, $window) {
    var service = this;
    var defaultDuration = 4000;

    service.alert = function (title,message, callback, isError) {
        if ($window.materialAlert) {
            materialAlert(title,message,callback, isError);
        }
    };

    service.confirm = function (title,message, callback) {
        if ($window.materialConfirm) {
            materialConfirm(title,message,callback);
        }
    };

    service.closeMaterialAlert = function(){
        if ($window.closeMaterialAlert) {
            closeMaterialAlert(e,result);
        }
    };

    service.closeAlert = function(close) {
        closeAlert();
    };

}]).service('$fileUploadService', ['$rootScope', '$window', function ($rootScope, $window) {
    var service = this;
    var defaultDuration = 4000;

    service.openFileModal = function (title, message, callback) {
        if ($window.showFileModal) {
            showFileModal(title, message, callback);
        }
    };

    service.closeMaterialAlert = function () {
        if ($window.closeFileModal) {
            closeFileModal(e);
        }
    };

}]).service('fileService',[function(){
    var service = this;
    service.file = null;
    service.push = function (file) {
        service.file = file;
    };
    service.getFile = function(){
      return service.file;
    };
}]).factory('httpAuthInterceptor', ['$q','$location', '$rootScope', 'authService', '$alertService','$timeout','appUtil',
    function ($q,$location, $rootScope, authService, $alertService,$window, appUtil) {
    return {
        request: function (config) {
            $rootScope.showSpinner = true;
            if( config.showSpinner != undefined && !config.showSpinner ){
                $rootScope.showSpinner = false;
            }
            config.headers.auth = authService.getAuthorization();
            if(config.method=="POST" && config.data == undefined){
                config["data"] = {};
            }
            checkForScmFilter($rootScope, $window, config, appUtil);
            return config;
        },
        requestError: function (rejection) {
            $rootScope.showSpinner = false;
            return $q.reject(rejection);
        },
        response: function (response) {
            $rootScope.showSpinner = false;
            return response || $q.when(response);
        },

        responseError: function (response) {
            $rootScope.showSpinner = false;
            if (response.status === 401) {
                if($rootScope.logout){
                    $rootScope.logout(true);
                }else{
                    $window.location = $window.location.href.split("#")[0];
                }
            }

            if(response.status === 406){
                if(response.data.errorCode === 701){
                    $alertService.alert(response.data.errorTitle,response.data.errorMsg,true);
                }
            }

            //Day close exception handler
            if(response.status === 409){
                if (response.data.errorMessage == "After Day Close No transactions will happen till 5'o clock in the morning..!") {
                    $alertService.alert("Please Acknowledge The Variance to do the Transaction..!",
                        "After Day Close No transactions will happen till 5'o clock in the morning..! <br><b>Please Acknowledge The Variance to do the Transaction..!</b> ");
                } else if (response.data.errorMessage!==undefined
                        && response.data.errorMessage !== null ) {
                    $alertService.alert(response.data.errorType,response.data.errorMessage);
                } else {
                    $alertService.alert("Day Close in Progress",
                        "Please wait for variance report to complete, before doing/cancelling any transaction!");
                }
                 return;
            }
            return $q.reject(response);
        }
    };
}]).factory('tokenizeInterceptor', ['TokenUtil', 'appUtil', function (TokenUtil, appUtil) {
    return {
        request: function (config) {
        //    console.log(config); //config.url,config.method, config.headers  //"http://localhost:8080/master-service/rest/v1/unit-metadata/all-units-list"
            var metadata = appUtil.getMetadata();
            if(!appUtil.checkEmpty(metadata) && !appUtil.checkEmpty(metadata.tokenizedApis)){
                var apis = metadata.tokenizedApis;
                var module = appUtil.getModule(config.url);
                if(apis.indexOf(module)>=0){
                    config.headers.apiToken = TokenUtil.getApiToken(module);
                }
            }
            return config;
        }
    };
}]).config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('httpAuthInterceptor');
    $httpProvider.interceptors.push('tokenizeInterceptor');

    /*$httpProvider.interceptors.push(['$q', 'version', function($q, version) {
        return {
            'request': function (request) {
                if (request.url.indexOf("views")!=-1 && request.url.substr(-5) == '.html') {
                    request.params = {
                        v: version
                    }
                }
                return $q.resolve(request);
            }
        }
    }]);*/

}]).run(['$rootScope', 'authService', '$state', '$stateParams', '$http', 'apiJson', 'TokenUtil','appUtil',
    function ($rootScope, authService, $state, $stateParams, $http, apiJson, TokenUtil,appUtil) {
        console.log("inside run function");
        $rootScope.v = 1;
        $rootScope.$state = $state;
        $rootScope.$stateParams = $stateParams;
        $rootScope.showSpinner = false;
        $rootScope.showOverlay = false;
        $rootScope.$on('$stateChangeStart', function (event, toState, toParams, fromState) {
            console.log("Inside the stateChangeStart function");
            $rootScope.aclData = appUtil.getAcl();
            $rootScope.currentState = toState;
            $rootScope.previousState = fromState;
            if (toState.name != "login" && toState.name != "signup") {
                if (authService.getAuthorization() == null || angular.isUndefined(authService.getAuthorization())) {
                    var params = {accessDenied: true};
                    //$state.go('login', params);
                }
            }
            var tokenApis = TokenUtil.getPageApiTokenMapping(toState.name);
            if(tokenApis !=null && tokenApis.length > 0){
                for (var i=0; i< tokenApis.length; i++){
                    var api = tokenApis[i];
                    $http({
                        method: "POST",
                        url: apiJson.urls.accessControl.generateApiToken
                    }).then(function(response) {
                        if(response != null){
                            TokenUtil.setTokenToApi(api, response.data);
                        }
                    }, function(error) {
                        console.log("Token generation failed. Please call support team.");
                    });
                }
            }
        });
    }
]).directive('stringToNumber', [function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModel) {
            ngModel.$parsers.push(function (value) {
                return '' + value;
            });
            ngModel.$formatters.push(function (value) {
                return parseFloat(value, 10);
            });
        }
    };
}]).directive('fileModel', ['$parse','fileService', function ($parse,fileService) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            //var model = $parse(attrs.fileModel);
            //var modelSetter = model.assign;
            element.bind('change', function () {
                scope.$apply(function () {
                    fileService.push(element[0].files[0]);
                    //modelSetter(scope, element[0].files[0]);
                });
            });
        }
    };

}]).directive('productView', [function () {

    return {
        restrict: 'E',
        scope: {
            scmProductList: "=scmProductList",
            listLabel: "=listLabel",
            fulfillmentFilter: "=fulfillmentFilter"
        },
        templateUrl: window.version+'scmProductViewDirective.html'
    };
}]).directive('ngPrint', [function(){

        var printSection = document.getElementById('printSection');

        // if there is no printing section, create one
        if (!printSection) {
            printSection = document.createElement('div');
            printSection.id = 'printSection';
            printSection.classList[0] = "avoidPageBreak";
            printSection.classList[1] = "printSection";
            document.body.appendChild(printSection);
        }

        function link(scope, element, attrs) {
            element.on('click', function () {
                var elemToPrint = document.getElementById(attrs.printElementId);
                if (elemToPrint) {
                    printElement(elemToPrint);
                    window.print();
                }
            });


            if (window.matchMedia) {
                var mediaQueryList = window.matchMedia('print');
                //console.log(mediaQueryList);
                mediaQueryList.addListener(function(mql) {
                    //console.log(mql);
                    if (mql.matches) {
                        //do nothing
                    } else {
                        afterPrint();
                    }
                });
            }

            window.onafterprint = afterPrint;
        }

        function afterPrint() {
            // clean the print section before adding new content
            printSection.innerHTML = '';
        }

        function printElement(elem) {
            // clones the element you want to print
            printSection.innerHTML = '';
            var domClone = elem.cloneNode(true);
            domClone.style.display = "block";
            printSection.appendChild(domClone);
            //console.log("DIV height ::::: ",$(printSection).height());
            printSection.style.height = $(printSection).height();
        }

        return {
            link: link,
            restrict: 'A'
        };

}]).filter('propsFilter', [function () {
    return function (items, props) {
        var out = [];
        if (angular.isArray(items)) {
            items.forEach(function (item) {
                var itemMatches = false;

                var keys = Object.keys(props);
                for (var i = 0; i < keys.length; i++) {
                    var prop = keys[i];
                    var text = props[prop].toLowerCase();
                    if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
                        itemMatches = true;
                        break;
                    }
                }
                if (itemMatches) {
                    out.push(item);
                }
            });
        } else {
            // Let the output be the input untouched
            out = items;
        }

        return out;
    };
}]).directive("aclMenu", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        var hasAcl = false;
        if(aclData!=null){
            var acls = attributes.aclMenu.split(',');
            if(acls.length>0){
                for(var i=0; i< acls.length;i++){
                    if(aclData.menu[acls[i]] != null){
                        hasAcl = true;
                        break;
                    }
                }
                if(aclData.menu!=null && hasAcl){
                    element.show();
                }else{
                    element.hide();
                }
            }
        }
        /*scope.$watch(attributes.acl, function(value, oldValue) {
         heart(value);
         }, true);*/
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive("aclSubMenu", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        var hasAcl = false;
        if(aclData!=null){
            var acls = attributes.aclSubMenu.split(',');
            if(acls.length>0){
                for(var i=0; i< acls.length;i++){
                    if(aclData.subMenu[acls[i]] != null){
                        hasAcl = true;
                        break;
                    }
                }
                if(aclData.subMenu!=null && hasAcl){
                    element.show();
                }else{
                    element.hide();
                }
            }
        }
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive("aclAction", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if(aclData!=null){
            if(aclData.action!=null && aclData.action[attributes.aclAction]!=null){
                element.show();
            }else{
                element.hide();
            }
        }
    }
    return({
        link: link,
        restrict: "A"
    });
}]).directive('hcChart', function () {
    return {
        restrict: 'E',
        template: '<div></div>',
        scope: {
            options: '='
        },
        link: function (scope, element) {
            Highcharts.chart(element[0], scope.options);
        }
    };
}).directive('stopEvent', function () {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            element.bind('keydown', function (e) {
                if (e.keyCode === 13) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    };
}).directive('draggable1', ['$document', function($document) {
    return {
        restrict: 'A',
        link: function(scope, elm, attrs) {
            var startX, startY, initialMouseX, initialMouseY;
            elm.css({ position: 'fixed' });
            elm.css("background-color", "#2d545e");
            elm.css("z-index", "999");
            elm.css("width", "165px");
            elm.css("padding", "8px");
            elm.css("border-radius", "10px");
            elm.bind('mousedown', function($event) {
                startX = elm.prop('offsetLeft');
                startY = elm.prop('offsetTop');
                initialMouseX = $event.clientX;
                initialMouseY = $event.clientY;
                $document.bind('mousemove', mousemove);
                $document.bind('mouseup', mouseup);
                return false;
            });

            function mousemove($event) {
                var dx = $event.clientX - initialMouseX;
                var dy = $event.clientY - initialMouseY;
                elm.css({
                    top: startY + dy + 'px',
                    left: startX + dx + 'px'
                });
                return false;
            }

            function mouseup() {
                $document.unbind('mousemove', mousemove);
                $document.unbind('mouseup', mouseup);
                elm.css({ position: 'fixed' });
                document.getElementById("_draggableInputId").focus();
            }
        }
    };
}]).directive('allowPattern', [function() {
    return {
        restrict: "A",
        compile: function(tElement, tAttrs) {
            return function(scope, element, attrs) {
                // I handle key events
                element.bind("keypress", function(event) {
                    var keyCode = event.which || event.keyCode; // I safely get the keyCode pressed from the event.
                    var keyCodeChar = String.fromCharCode(keyCode); // I determine the char from the keyCode.

                    // If the keyCode char does not match the allowed Regex Pattern, then don't allow the input into the field.
                    if (!keyCodeChar.match(new RegExp(attrs.allowPattern, "i"))) {
                        event.preventDefault();
                        return false;
                    }

                });
            };
        }
    };
}]).filter("groupBy",["$parse","$filter",function($parse,$filter){
    return function(array,groupByField){
        var result	= [];
        var prev_item = null;
        var groupKey = false;
        var filteredData = $filter('orderBy')(array,groupByField);
        for(var i=0;i<filteredData.length;i++){
            groupKey = false;
            if(prev_item !== null){
                if(prev_item[groupByField] !== filteredData[i][groupByField]){
                    groupKey = true;
                }
            } else {
                groupKey = true;
            }
            if(groupKey){
                filteredData[i]['group_by_key'] =true;
            } else {
                filteredData[i]['group_by_key'] =false;
            }
            result.push(filteredData[i]);
            prev_item = filteredData[i];
        }
        return result;
    }
}]).run(function() {
        var touchStartY = 0;

        document.addEventListener('touchstart', function(event) {
            touchStartY = event.touches[0].clientY; // Store initial touch Y position
        }, { passive: false });

        document.addEventListener('touchmove', function(event) {
            if (window.scrollY === 0 && event.touches[0].clientY > touchStartY) {
                event.preventDefault(); // Block pull-down gesture
            }
        }, { passive: false });
    });;

    function checkForScmFilter($rootScope, $window, config, appUtil) {
        var scmFilter = null;
        if (typeof localStorage !== 'undefined' && localStorage.getItem) {
            scmFilter = localStorage.getItem('supScmAdmnFilter');
        } else {
            console.warn("localStorage is not available.");
        }
        if (appUtil.isEmptyObject(scmFilter) || scmFilter === "true") {
            $rootScope.supScmAdmnFilter = true;
        } else if (scmFilter === "false") {
            $rootScope.supScmAdmnFilter = false;
        } else {
            $rootScope.supScmAdmnFilter = true; // default
        }
        if ($rootScope.supScmAdmnFilter) {
            setCompanyInHeader($rootScope, $window, config, appUtil);
        }
    }

    function setCompanyInHeader($rootScope, $window, config, appUtil) {
        if(appUtil.getUnitData()!=null){
            var companyId = appUtil.getUnitData().company.id;
            if (companyId && companyId !== "null") {
                config.headers['companyId'] = companyId;
            }
        }
    }
