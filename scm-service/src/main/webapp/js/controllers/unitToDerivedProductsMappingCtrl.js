
scmApp
    .controller('unitToDerivedProductsMappingCtrl',
        [ '$scope', '$http', 'ScmApiService', 'toast', 'apiJson', '$alertService', '$timeout', 'Popeye',
            function ($scope, $http, ScmApiService, toast, apiJson, $alertService, $timeout, Popeye) {

            $scope.initUnitToDerivedProductsMapping = function () {
                $scope.mappingTypeList = [
                    { id: 1, name: 'Unit to Product Mapping', type: 'UNIT_PRODUCT_MAPPING' },
                    { id: 2, name: 'Product to Unit Mapping', type: 'PRODUCT_UNIT_MAPPING' }
                ];
                $scope.selectedMappingType = null;
                $scope.unitsList = [];
                $scope.selectedUnit = null;
                $scope.productsList = [];
                $scope.selectedProduct = null;
                $scope.fulfillmentTypes = [
                    { id: 'KITCHEN', value: 'KITCHEN' },
                    { id: 'WAREHOUSE', value: 'WAREHOUSE' },
                    { id: 'EXTERNAL', value: 'EXTERNAL' },
                    { id: 'INTERNAL', value: 'INTERNAL' }
                ];
            }

            $scope.selectMappingType = function (mappingType) {
                $scope.selectedMappingType = mappingType;
                resetSelectedValue();
                if (mappingType.id === 1) {
                    fetchUnits();
                } else if (mappingType.id === 2) {
                    fetchProducts();
                }
            }

            function resetSelectedValue() {
                $scope.valueSelected = null;
                $scope.valueDataList = [];
                $scope.valueSelectedList = [];
                $scope.cloningMapping = null;
                $scope.productsGrid = {
                    data: []
                };
                $timeout(function() {
                    $('#uiSelect1').trigger('change');
                    $('#cloneDisplayData').trigger('change');
                }, 0);
            }

            function fetchUnits() {
                var url = apiJson.urls.skuMapping.getAllUnit;
                ScmApiService.get(url).then(function (responseData) {
                    $scope.valueDataList = responseData;
                });
            }

            function fetchProducts() {
                var url = apiJson.urls.productManagement.getAllDerivedProducts;
                var params = {
                    fulfillmentType: 'DERIVED',
                }
                ScmApiService.get(url, params).then(function (responseData) {
                    $scope.valueDataList = responseData.data;
                });
            }

            $scope.onChangeValue = function (value) {
                $scope.cloningMapping = null;
                $timeout(function() {
                    $('#cloneDisplayData').trigger('change');
                }, 0);
            }

            $scope.searchMappingShow = function(id) {
                if(id === undefined || id === null) {
                    toast.warning("Please select a valid Unit or Product to view the mapping.");
                    return;
                }
                $scope.showName = $scope.valueSelected.name;
                $scope.valueSelectedList = [];
                if ($scope.selectedMappingType.id === 1) {
                    fetchDerivedProductsForUnit(id);
                } else {
                    fetchUnitsForProduct(id);
                }
            }

            function fetchDerivedProductsForUnit(unitId) {
                var url = apiJson.urls.productManagement.getDerivedProductsMappingForUnit;
                var params = {
                    unitId: unitId
                }
                ScmApiService.get(url, params).then(function (responseData) {
                    if(responseData.success === false) {
                        toast.warning(responseData.message, 6000);
                        return;
                    }
                    $scope.valueSelectedList = responseData.data;
                    $scope.productsGrid = $scope.productGridOptions();
                    addDefaultValues();
                });
            }

            function fetchUnitsForProduct(productId) {
                var url = apiJson.urls.productManagement.getAllUnitsForProductMapping;
                var params = {
                    productId: productId
                }
                ScmApiService.get(url, params).then(function (responseData) {
                    $scope.valueSelectedList = responseData.data;
                    $scope.productsGrid = $scope.unitGridOptions();
                    addDefaultValues();
                });
            }

            function addDefaultValues() {
                $scope.valueSelectedList.forEach(function (item) {
                    item.oldFulfillmentType = item.fulfillmentType;
                });
                $scope.productsGrid.data = $scope.valueSelectedList;
            }

            $scope.productGridOptions = function () {
                return {
                    enableColumnMenus: false,
                    enableFiltering: true,
                    enableCellEditOnFocus: true,
                    enableColumnResizing: true,
                    rowHeight: 40,
                    paginationPageSizes: [10, 20, 30, 40, 50],
                    paginationPageSize: 20,
                    useExternalPagination: false,
                    rowTemplate: "<div ng-class=\"{'highlight-row': row.entity._highlight}\" ng-repeat=\"(colRenderIndex, col) in colContainer.renderedColumns track by col.colDef.name\" class=\"ui-grid-cell\" ui-grid-cell></div>",
                    columnDefs: [{
                        field: 'productId',
                        displayName: 'Product Id',
                        enableCellEdit: false,
                        width: 200
                    }, {
                        field: 'productName',
                        displayName: 'Product Name',
                        enableCellEdit: false
                    }, {
                        field: 'productCategory',
                        displayName: 'Product Category',
                        enableCellEdit: false,
                        width: 200
                    }, {
                        field: 'fulfillmentType',
                        name: 'fulfillmentType',
                        enableCellEdit: true,
                        editableCellTemplate: 'ui-grid/dropdownEditor',
                        editDropdownOptionsArray: $scope.fulfillmentTypes,
                        editDropdownIdLabel: 'id',
                        editDropdownValueLabel: 'value',
                        displayName: 'Fulfillment Type',
                        width: 200
                   }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            if (colDef.field === 'fulfillmentType') {
                                rowEntity._highlight = (newValue !== rowEntity.oldFulfillmentType);
                            }
                            $scope.$apply();
                        });
                    }
                };
            };

            $scope.unitGridOptions = function () {
                return {
                    enableColumnMenus: false,
                    enableFiltering: true,
                    enableCellEditOnFocus: true,
                    enableColumnResizing: true,
                    rowHeight: 40,
                    paginationPageSizes: [10, 20, 30, 40, 50],
                    paginationPageSize: 20,
                    useExternalPagination: false,
                    rowTemplate: "<div ng-class=\"{'highlight-row': row.entity._highlight}\" ng-repeat=\"(colRenderIndex, col) in colContainer.renderedColumns track by col.colDef.name\" class=\"ui-grid-cell\" ui-grid-cell></div>",
                    columnDefs: [{
                        field: 'unitId',
                        displayName: 'Unit Id',
                        enableCellEdit: false,
                        width: 200
                    }, {
                        field: 'unitName',
                        displayName: 'Unit Name',
                        enableCellEdit: false
                    }, {
                        field: 'unitCategory',
                        displayName: 'Unit Category',
                        enableCellEdit: false,
                        width: 200
                    }, {
                        field: 'fulfillmentType',
                        name: 'fulfillmentType',
                        enableCellEdit: true,
                        editableCellTemplate: 'ui-grid/dropdownEditor',
                        editDropdownOptionsArray: $scope.fulfillmentTypes,
                        editDropdownIdLabel: 'id',
                        editDropdownValueLabel: 'value',
                        displayName: 'Fulfillment Type',
                        width: 200
                    }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            if (colDef.field === 'fulfillmentType') {
                                rowEntity._highlight = (newValue !== rowEntity.oldFulfillmentType);
                            }
                            $scope.$apply();
                        });
                    }
                };
            };

            $scope.filterSelectedValue = function (value) {
                if ($scope.valueSelected == undefined || $scope.valueSelected == null) {
                    return true;
                }
                return value.id != $scope.valueSelected.id;
            };

            $scope.makeClone = function() {
                if( $scope.cloningMapping == undefined || $scope.cloningMapping == null ) {
                    toast.warning("Please select a mapping to clone.");
                    return;
                }
                var data = {
                    mappingType: $scope.selectedMappingType,
                    valueSelected: $scope.valueSelected,
                    cloningMapping: $scope.cloningMapping
                }

                var mappingModal = Popeye.openModal({
                    templateUrl: "cloneDisplayModal.html",
                    controller: "cloneDisplayModalCtrl",
                    resolve: {
                        data: function () {
                            return data;
                        }
                    },
                    click: false,
                    keyboard: true
                });
                mappingModal.closed.then(function (result) {
                    if (result) {
                        resetSelectedValue();
                        $scope.selectedMappingType = null;
                        $timeout(function() {
                            $('#mappingTypeSelection').trigger('change');
                        }, 0);
                    }
                });
            }

            $scope.verifyAndSubmit = function () {
                $alertService.confirm("Please select YES or NO", "Updating Derived products Mapping", function (result) {
                    if (result) {
                        submitMappingChanges();
                    }
                });
            }

            function submitMappingChanges() {
                var changes = [];
                var unitId = $scope.valueSelected.id;
                var productId = $scope.valueSelected.id;
                for (var i = 0; i < $scope.valueSelectedList.length; i++) {
                    var item = $scope.valueSelectedList[i];
                    if ( item.fulfillmentType != item.oldFulfillmentType ) {
                        var change = {
                            mappingId: item.mappingId,
                            fulfillmentType: item.fulfillmentType,
                            unitId: $scope.selectedMappingType.id === 1 ? unitId : item.unitId,
                            productId: $scope.selectedMappingType.id === 2 ? productId : item.productId,
                        }
                        changes.push(change);
                    }
                }
                if (changes.length === 0) {
                    toast.create('No changes made to the fulfillment types.');
                    return;
                }
                var url = apiJson.urls.productManagement.updateDerivedMappingData;
                var data = changes;
                ScmApiService.post(url, data).then(function (responseData) {

                    if( responseData.success ) {
                        toast.create(responseData.message, 6000);
                    } else {
                        toast.error(responseData.message, 6000);
                        $alertService.alert(responseData.message, responseData.data, null, false);
                    }
                    $scope.productsGrid.data = [];
                    resetSelectedValue();
                    $scope.selectedMappingType = null;
                    $timeout(function() {
                        $('#mappingTypeSelection').trigger('change');
                    }, 0);
                });
            }


        }
    ]).controller('cloneDisplayModalCtrl', ['$scope', 'data', 'ScmApiService', 'toast', 'apiJson', '$timeout', 'Popeye',
        function ($scope, data, ScmApiService, toast, apiJson, $timeout, Popeye) {
            // Initialize the controller
            $scope.initCloneDisplayModal = function () {
                $scope.mappingType = data.mappingType;
                $scope.valueSelected = data.valueSelected;
                $scope.cloningMapping = data.cloningMapping;
                $scope.currentPage = 1;
                $scope.pageSize = 10;
                getMappingFrom_To();
            }

            function getMappingFrom_To() {
                $scope.cloningMappingList = [];
                if ($scope.mappingType.id === 1) {
                    getCloningForUnits();
                } else if ($scope.mappingType.id === 2) {
                    getCloningForProducts();
                } else {
                    toast.warning("Invalid mapping type selected.");
                    return;
                }
            }

            function getCloningForUnits() {
                var url = apiJson.urls.productManagement.getDerivedProductCloningForUnits;
                var params = {
                    fromUnitId: $scope.cloningMapping.id
                }
                ScmApiService.get(url, params).then(function (responseData) {
                    if( responseData.data.length === 0 ) {
                        toast.warning(responseData.message);
                        Popeye.closeCurrentModal(true);
                        return;
                    }
                    $scope.cloningMappingList = responseData.data;
                    $scope.updatePagination();
                });
            }

            function getCloningForProducts() {
                var url = apiJson.urls.productManagement.getUnitsCloningForProduct;
                var params = {
                    fromProductId: $scope.cloningMapping.id
                }
                ScmApiService.get(url, params).then(function (responseData) {
                    if( responseData.data.length === 0 ) {
                        toast.warning(responseData.message);
                        Popeye.closeCurrentModal(true);
                        return;
                    }
                    $scope.cloningMappingList = responseData.data;
                    $scope.updatePagination();
                });
            }

            $scope.updatePagination = function () {
                if (!$scope.cloningMappingList) return;
                var start = ($scope.currentPage - 1) * $scope.pageSize;
                var end = start + $scope.pageSize;
                $scope.paginatedList = $scope.cloningMappingList.slice(start, end);
            };

            // Watch for data load
            $scope.$watch('cloningMappingList', function (newVal) {
                if (newVal && newVal.length > 0) {
                    $scope.currentPage = 1;
                    $scope.updatePagination();
                }
            });

            $scope.submitClone = function () {
                if (!$scope.cloningMappingList || $scope.cloningMappingList.length === 0) {
                    toast.warning("No mappings available to clone.");
                    return;
                }
                var url = apiJson.urls.productManagement.cloneDerivedProductMappings;
                var params = {
                    valueType: $scope.mappingType.type,
                    valueId: $scope.valueSelected.id
                }
                ScmApiService.post(url, $scope.cloningMappingList, params).then(function (responseData) {
                    if (responseData.success) {
                        toast.create(responseData.message);
                        Popeye.closeCurrentModal(true);
                    } else {
                        toast.error(responseData.message);
                        Popeye.closeCurrentModal(false);
                    }
                });
            }

        }
    ]);
