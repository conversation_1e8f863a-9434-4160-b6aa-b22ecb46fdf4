/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 *
 * Created by shikhar on 23-05-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('mapPackagingProductCtrl', ['$http', '$rootScope', '$scope', 'authService', '$location', '$state',
        'appUtil', '$stateParams', 'metaDataService', '$toastService', 'apiJson', 'packagingService', 'productService','ScmApiService',
        function ($http, $rootScope, $scope, authService, $location, $state, appUtil, $stateParams, metaDataService,
                  $toastService, apiJson, packagingService, productService, ScmApiService) {

            $scope.selectOptions = {width: '100%'};
            $scope.products = [];
            $scope.definitions = {
                selected: null,
                lists: {"CASE": [], "INNER": [], "LOOSE": []}
            };
            $scope.skuMappings = [];
            $scope.selectedProfiles = {};
            $scope.mappedProfiles = [];
            $scope.checkEmpty = appUtil.checkEmpty;

            function getAllProducts() {
                $scope.products = appUtil.getScmProductDetails();
            }

            function getAllPackagingProfiles() {
                packagingService.getAllProfiles(function (profiles) {
                    $scope.definitions.lists = profiles;
                });
            }

            function getAllMappings() {
                metaDataService.getAllPackagingMappings(function (mappingsCacheData) {
                    if(mappingsCacheData!=null){
                        $scope.skuMappings = mappingsCacheData;
                        if(!$scope.checkEmpty($scope.productId)){
                            $scope.selectProduct($scope.productId);
                        }
                    }else{
                        $toastService.create("Error loading product packaging mappings.");
                    }
                });
            }

            function prepareProductMappings(selectedProfiles) {
                var mappingObject = [];

                angular.forEach(selectedProfiles, function (profile) {
                    mappingObject.push({
                        packagingId: profile.packagingId,
                        productId: $scope.productId,
                        mappingStatus: "ACTIVE"
                    });
                });
                return mappingObject;
            }

            $scope.init = function() {
//                getAllProducts();
                getAllProductsBasicdetails()
                getAllPackagingProfiles();
                getAllMappings();
            };

            function getAllProductsBasicdetails() {
                ScmApiService
                .get(apiJson.urls.productManagement.getProducts)
                .then(function (responseData) {
                    if(!appUtil.checkEmpty(responseData) && !appUtil.checkEmpty(responseData.data)) {
                        $scope.products = Object.values(responseData.data);
                        $scope.allProducts = $scope.products;
                        if($scope.stateProductId != null) {
                            $scope.selectProduct(null, $scope.stateProductId);
                        }
                    } else {
                        $toastService.create("Products not found");
                    }
                })
            }

            $scope.markDefault =  function(mappingId){
                $http({
                    method:"PUT",
                    url:apiJson.urls.productManagement.defaultProductPackaging,
                    data: mappingId
                }).then(function (response) {
                    var message = !response.data ? "Update failed. Try again later." : "Update Successful.";
                    $toastService.create(message);
                    updateCacheAndReflect("SET_DEFAULT",mappingId);
                }, function (response) {
                    console.log("Error in posting data", response);
                });
            };

            $scope.updateStatus = function(id,activate) {
                $http({
                    method:"PUT",
                    url: activate ? apiJson.urls.productManagement.activateMapping : apiJson.urls.productManagement.deactivateMapping,
                    data: id
                }).then(function (response) {
                    var message = !response.data ? "Update failed. Try again later." : "Update Successful.";
                    $toastService.create(message);
                    updateCacheAndReflect("UPDATE",id,activate);
                }, function (response) {
                    console.log("Error in posting data", response);
                });
            };

            $scope.selectProduct = function (productId) {
                $scope.productId = productId;
                $scope.mappedProfiles = [];
                var mappings = $scope.skuMappings[productId];
                if (!appUtil.checkEmpty(mappings)) {
                    for (var index in mappings) {
                        $scope.mappedProfiles.push(mappings[index]);
                    }
                    console.log("Mapped Profiles after massaging is ", $scope.mappedProfiles);
                }
            };

            $scope.getProfileName = function(id){
                var profile = packagingService.getProfile(id);
                return profile.packagingName;
            };


            function checkInMappedProfiles(item, mappedProfiles) {
                if(item!=undefined){
                    return mappedProfiles.filter(function(profile){
                        return profile.packagingId == item.packagingId;
                    }).length>0;
                }else{
                    return false;
                }
            }

            $scope.onDrop = function (list, item) {
                if(!checkInMappedProfiles(item,$scope.mappedProfiles)){
                    list[item.packagingId] = item;
                }

                packagingService.addSubPackagings(list,item,$scope.onDrop);
            };

            $scope.checkIfExists = function (list, item, index) {
                var flag = true;
                for (var pos in list) {
                    if (list[pos].packagingId == item.packagingId) {
                        flag = false;
                    }
                }
                return flag;
            };

            $scope.removeFromSelected = function(profiles,id){
               delete profiles[id];
            };

            $scope.submit = function () {
                console.log("selected profiles ::::",$scope.selectedProfiles);
                if(!$scope.checkEmpty($scope.productId) && !$scope.checkEmpty($scope.selectedProfiles)){
                    var reqObj = prepareProductMappings($scope.selectedProfiles);
                    $http({
                        method:"POST",
                        url: apiJson.urls.productManagement.packagingMapping,
                        data: reqObj
                    }).then(function (response) {
                        if(response.data!=null){
                            $toastService.create("Update Successful.");
                            updateCacheAndReflect("ADD",response.data);
                        }else{
                            $toastService.create("Update failed. Try again later.");
                        }
                    }, function (response) {
                        if(response.data.errorCode!=null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg,function () {}, true);
                        }
                        console.log("Error in posting data", response);
                    });
                }else{
                    if($scope.checkEmpty($scope.productId)){
                        $toastService.create("Please select a product first!");
                    }else if($scope.checkEmpty($scope.selectedProfiles)){
                        $toastService.create("Please select a profile first!");
                    }
                }
            };

            function updateCacheAndReflect(type,data, activate) {
                metaDataService.getAllPackagingMappings(function (mappingsCacheData) {
                    if(mappingsCacheData!=null){
                        var mappings = mappingsCacheData[$scope.productId];
                        if(type=="ADD"){
                            if(mappings!=null){
                                mappings = mappings.concat(data);
                                mappingsCacheData[$scope.productId] = mappings;
                                $scope.mappedProfiles = mappings;
                            }else{
                                mappingsCacheData[$scope.productId] = data;
                            }
                        }
                        if(type=="SET_DEFAULT"){
                            mappings.map(function (mapping) {
                                mapping.isDefault = mapping.productPackagingMappingId==data;
                            });
                            $scope.mappedProfiles = mappings;
                            mappingsCacheData[$scope.productId] = mappings;
                        }
                        if(type=="UPDATE"){
                            mappings.map(function (mapping) {
                                mapping.productPackagingMappingId==data?(mapping.mappingStatus = activate?"ACTIVE":"IN_ACTIVE"):null;
                            });
                            $scope.mappedProfiles = mappings;
                        }
                        appUtil.getMetadata().packagingMappings = mappingsCacheData;
                        appUtil.setMetadata(appUtil.getMetadata());
                        $scope.skuMappings = appUtil.getMetadata().packagingMappings;
                        $scope.selectedProfiles = {};
                    }else{
                        $toastService.create("Error loading product packaging mappings.");
                    }
                });
            }
        }
    ]
);