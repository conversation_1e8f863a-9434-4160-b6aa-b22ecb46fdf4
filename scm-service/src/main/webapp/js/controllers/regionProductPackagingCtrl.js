angular.module('scmApp').controller('regionProductPackagingCtrl',
    ['$scope', 'ScmApiService', 'apiJson', 'toast', '$alertService',
    function ($scope, ScmApiService, apiJson, toast, $alertService) {

        $scope.init = function() {
            $scope.regions = [];
            $scope.selectedRegions = [];

            $scope.allProducts = [];
            $scope.selectedProducts = [];

            $scope.regionProductPackagingData = [];
            $scope.productPackagings = [];

            getAllRegions();
            getAllProducts();

            $scope.paginatedList = [];
            $scope.pageSize = 10;
            $scope.currentPage = 1;

        }

        $scope.updatePagination = function () {
            if ( !$scope.regionProductPackagingData ) return;
            var start = ($scope.currentPage - 1) * $scope.pageSize;
            var end = start + $scope.pageSize;
            $scope.paginatedList = $scope.regionProductPackagingData.slice(start, end);
        };

        function getAllRegions() {
            var url = apiJson.urls.unitMetadata.regions;
            ScmApiService.get(url).then(function(responseData) {
                $scope.regions = responseData;
            });
        }

        function getAllProducts() {
            var url = apiJson.urls.productManagement.getProducts;
            ScmApiService.get(url).then(function(responseData) {
                var productMap = responseData.data;
                $scope.allProducts = Object.keys(productMap).map(function (key) {
                    return productMap[key];
                });
            });
        }

        $scope.multiSelectSettings1 = {
            enableSearch: true,
            scrollable: true,
            scrollableHeight: '250px',
            idProperty: 'id',
            displayProp: 'label',
            clearSearchOnClose: true
        };

        $scope.multiSelectSettings2 = {
            enableSearch: true,
            scrollable: true,
            scrollableHeight: '250px',
            idProperty: 'id',
            displayProp: 'name',
            showCheckAll: false,
            clearSearchOnClose: true
        };

        // convert region strings into multiselect format
        function getAllRegions() {
            var url = apiJson.urls.unitMetadata.regions;
            ScmApiService.get(url).then(function (responseData) {
                $scope.regions = responseData; // strings like ["PUNJAB", "PUNE", ...]
                $scope.regionsDropdown = $scope.regions.map(function (item, index) {
                    return { id: item, label: item };
                });
            });
        }

        $scope.searchMappings = function() {
            if( !$scope.selectedProducts.length ) {
                toast.warning("Please select at least one product.");
                return;
            }
            var data = {
                regionCodes: $scope.selectedRegions.map(function(region) { return region.label; }),
                productIds: $scope.selectedProducts.map(function(product) { return product.id; })
            };
            getMappings(data, function () {
                getPackagingMappings(data, function () {
                    mapPackagingToProducts();
                });
            });
        }

        function getMappings(data, callback) {
            var url = apiJson.urls.productManagement.getRegionProductPackagings;
            ScmApiService.post(url, data).then(function(responseData) {
                $scope.regionProductPackagingData = responseData.data;
                if (callback) callback();
            });
        }

        function getPackagingMappings(data, callback) {
            var url = apiJson.urls.productManagement.getProductPackagingMapping;
            ScmApiService.post(url, data).then(function(responseData) {
                $scope.productPackagings = responseData.data;
                if (callback) callback();
            });
        }

        function mapPackagingToProducts() {
            for (var i = 0; i < $scope.regionProductPackagingData.length; i++) {
                var rpm = $scope.regionProductPackagingData[i];
                var pp = $scope.productPackagings[rpm.productId];
                rpm.oldPackagingId = rpm.packagingId;
                rpm.packagings = pp;
            }
            $scope.updatePagination();
        }

        $scope.verifyAndSubmitChanges = function() {
            $alertService.confirm("Are you sure you want to submit the changes?","", function(result) {
                if(result) {
                    submitChanges();
                }
            });
        }

        function submitChanges() {
            var url = apiJson.urls.productManagement.mapRegionProductPackaging;
            var data = [];
            for(var i = 0; i < $scope.regionProductPackagingData.length; i++) {
                var rpm = $scope.regionProductPackagingData[i];
                if (rpm.packagingId != null && rpm.packagingId != rpm.oldPackagingId) {
                    var d = {
                        id: rpm.id,
                        regionCode: rpm.regionCode,
                        productId: rpm.productId,
                        packagingId: rpm.packagingId,
                        status: rpm.status,
                        createdBy: rpm.createdBy
                    };
                    data.push(d);
                }
            }

            if( data.length == 0) {
                toast.warning("No changes found to submit.");
                return;
            }
            ScmApiService.post(url, data).then(function(responseData) {
                toast.success("Packaging mappings updated successfully.");
                $scope.init();
            });
        }

    }
]);