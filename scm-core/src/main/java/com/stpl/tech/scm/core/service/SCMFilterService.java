package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.scm.domain.model.UnitBrandFilterRequest;

public interface SCMFilterService {
    ApiResponse filterProductBasicDetail(UnitBrandFilterRequest filterRequest);

    boolean isExternalCompany(Integer companyId);

    boolean isCurrentUserMappedToACL(String aclKey);

    Integer getCompanyIdWithFilterChecks();

}
