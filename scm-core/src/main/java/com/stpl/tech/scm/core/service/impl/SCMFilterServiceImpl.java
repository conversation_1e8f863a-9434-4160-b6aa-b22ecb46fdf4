package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMFilterService;
import com.stpl.tech.scm.core.util.SCMConstant;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.UnitBrandFilterRequest;
import com.stpl.tech.util.domain.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SCMFilterServiceImpl implements SCMFilterService {

    @Autowired
    private SCMProductManagementService scmProductManagementService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public ApiResponse filterProductBasicDetail(UnitBrandFilterRequest filterRequest) {
        List<Integer> mappedBrands = getBrandIdsWithFilterChecks();
        if (Objects.isNull(mappedBrands) || mappedBrands.isEmpty()) {
            return scmProductManagementService.getProductBasicDetail();
        }

        Boolean autoProduction = filterRequest.getAutoProduction();
        Map<Integer, IdCodeName> productBasicDetail = scmCache.getProductDefinitions().values().stream()
                .filter(p -> mappedBrands.contains(p.getBrandId()))
                .filter(p -> autoProduction == null || autoProduction.equals(p.isAutoProduction()))
                .collect(Collectors.toMap(
                        ProductDefinition::getProductId,
                        p -> new IdCodeName(p.getProductId(), p.getUnitOfMeasure(), p.getProductName())
                ));

        return new ApiResponse(productBasicDetail);
    }

    @Override
    public List<Integer> getBrandIdsWithFilterChecks() {
        Integer companyId = RequestContext.getContext().getCompanyId();
        if(Objects.isNull(companyId)) {
            return null;
        }

        boolean isExternalCompany = isExternalCompany(companyId);

        if(isExternalCompany) {
            return null;
        }
        return SCMUtil.getMappedBrands(companyId);
    }

    @Override
    public boolean isExternalCompany(Integer companyId) {
        if(Objects.isNull(companyId) || companyId == 0) {
            return false;
        }
        Company company = masterDataCache.getCompany(companyId);
        return company != null && SCMConstant.EXTERNAL_COMPANY_TYPE.equals(company.getCompanyType());
    }

    @Override
    public boolean isCurrentUserMappedToACL(String aclKey) {
        Integer applicationId = SCMConstant.SCM_APPLICATION_ID;
        Integer loggedInUserId = RequestContext.getContext().getLoggedInUserId();
        return Optional.ofNullable(masterDataCache.getACLEmployeeMappingById(applicationId))
                .map(mapping -> mapping.getOrDefault(aclKey, Collections.emptySet()))
                .map(mappedUsers -> mappedUsers.contains(loggedInUserId))
                .orElse(false);
    }

}
