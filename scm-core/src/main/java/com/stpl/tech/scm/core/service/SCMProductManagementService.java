package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.RPPMappingRequest;
import com.stpl.tech.scm.domain.model.RegionProductPackagingMapping;
import com.stpl.tech.scm.domain.model.RequestedSkuDetails;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitDerivedProductMappingDto;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitProductsVO;
import com.stpl.tech.scm.domain.model.UserRequestDto;
import com.stpl.tech.scm.domain.model.VendorDetail;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
public interface SCMProductManagementService {

    public ProductDefinition viewProduct(int productId);

    public List<ProductDefinition> viewAllProducts(Integer unitId, boolean getArchived);

    public Map<Integer, ProductDefinition> getAllProductMaps();

    public Map<Integer, ProductDefinition> getAllVariantProductMaps();

    public ProductDefinition addNewProduct(ProductDefinition productDefinition , Integer userId) throws Exception;
    public ProductDefinition addNewProductV2(ProductDefinition productDefinition , Integer userId) throws DataUpdationException, SumoException;
    ProductDefinition cancelProduct(ProductDefinition productDefinition, Integer loggedInUser) throws SumoException;
    public ProductDefinition updateProduct(ProductDefinition productDefinition,Integer userId) throws DataUpdationException, SumoException;

    public ProductDefinition updateProductV2(ProductDefinition productDefinition,Integer userId) throws DataUpdationException, SumoException;

    public List<ProductDefinition> getAllUserCreatedProducts(UserRequestDto productRequest);

    public boolean activateProduct(int productId) throws DataUpdationException;

    public boolean deactivateProduct(int productId);

    public boolean archiveProduct(int productId);

    public List<PackagingDefinition> viewAllPackaging();

    public Map<Integer, PackagingDefinition> getAllPackagingMap();

    public PackagingDefinition viewPackaging(int packagingId);

    public boolean addNewPackaging(List<PackagingDefinition> packagingDefinition) throws SumoException;

    public boolean updatePackaging(PackagingDefinition packagingDefinition);

    public boolean deactivatePackaging(int packagingDefinitionId);

    public boolean activatePackaging(int packagingDefinitionId);

    public Map<String, Set<Map<String, Integer>>> getAllSubPackagingMapping();

    public Map<Integer, List<ProductPackagingMapping>> viewAllProductPackagingMapping();

    public Map<Integer, List<UnitProductPackagingMapping>> viewUnitProductPackagingMapping(int unitId);

    public ProductPackagingMapping viewProductPackagingMapping(int productPackagingMappingId);

    public List<ProductPackagingMapping> addNewProductPackagingMapping(List<ProductPackagingMapping> productPackagingMappings) throws SumoException;

    public boolean updateProductPackagingMapping(List<ProductPackagingMapping> productPackagingMappings);

    public boolean deactivateProductPackagingMapping(int productPackagingMappingId);

    public boolean activateProductPackagingMapping(int productPackagingMappingId);

    public List<ProductPackagingMapping> getProductPackagingMappingByProduct(int productId);

    public boolean setDefaultProductPackaging(int productPackagingMappingId) throws DataUpdationException;

    public SkuDefinition viewSku(int skuId) throws SumoException;

    public List<SkuDefinition> viewAllSku();

    public SkuDefinition addNewSku(SkuDefinition skuDefinition , Integer userId) throws Exception;

    public boolean addNewSkuV2(SkuDefinition skuDefinition , Integer userId) throws Exception;

    public List<RequestedSkuDetails> getAllUserCreatedSkus(UserRequestDto productRequest) throws Exception;
    public SkuDefinition updateSku(SkuDefinition skuDefinition , Integer userId) throws Exception;

    public SkuDefinition updateSkuV2(SkuDefinition skuDefinition, Integer userId) throws Exception;

    public boolean cancelSkuByUser(SkuDefinition skuDefinition , Integer userId) throws Exception;

    public boolean activateSku(int skuId);

    public boolean deactivateSku(int skuId);

    public Map<Integer, List<SkuPackagingMapping>> viewAllSkuPackagingMapping();

    public SkuPackagingMapping viewSkuPackagingMapping(int skuPackagingMappingId);

    public List<SkuPackagingMapping> addNewSkuPackagingMapping(List<SkuPackagingMapping> skuPackagingMappings) throws SumoException;

    public boolean updateSkuPackagingMapping(List<SkuPackagingMapping> skuPackagingMappings);

    public boolean activateSkuPackagingMapping(int skuPackagingMappingId);

    public boolean deactivateSkuPackagingMapping(int skuPackagingMappingId);

    public boolean setDefaultSkuPackaging(int skuPackagingMappingId) throws DataUpdationException;

    public Map<Integer, List<SkuAttributeValue>> viewAllSkuAttributeValues();

    public SkuAttributeValue viewSkuAttributeValue(int skuAttributeValueId);

    public boolean addNewSkuAttributeValues(List<SkuAttributeValue> skuAttributeValues) throws SumoException;

    public boolean updateSkuAttributeValues(List<SkuAttributeValue> skuAttributeValues);

    public boolean activateSkuAttributeValue(int skuAttributeValueId);

    public boolean deactivateSkuAttributeValue(int skuAttributeValueId);

    public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct();

    Map<Integer, List<SkuDefinition>> viewAllSkusByProductId();

    public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct(int productId);

    public List<VendorDetail> getAllVendorDetails();

	public List<VendorDetail> getUnitVendorDetails(int unitId);

	/**
	 * @return
	 */
	public List<ProductBasicDetail> viewAllBasicProducts(Integer unitId, boolean getArchived);

	/**
	 * @return
     * @param isScm
     */
	public List<ProductBasicDetail> viewAllBasicProductsForRecipe(Boolean isScm);

    public List<ProductDefinition> viewAllProductsForCafeInventory();

    public Map<Integer, Set<VendorDetail>> getUnitProductVendors(UnitProductsVO request) throws SumoException;

    public String uploadProductImage(MimeType mimeType, Integer userId, MultipartFile file) throws SumoException;

    public String uploadSkuImage(MimeType mimeType, Integer skuId, MultipartFile file) throws SumoException;

    public PlanOrderItem getProductForUnit(int productId, int unitId) throws SumoException;

	List<SkuDefinition> viewAllSku(int productId);

    public Map<Integer, Integer> getUnitSkuPackagingMappings(int unitId);

    public Map<Integer,List<SkuDefinition>> viewAllActiveSkuByUnitId(Integer unitId , List<Integer> productIds) throws SumoException;

    public Boolean updateSubCategoryShelfLife(Integer id , Integer defaultShelfLife , String range);

    ApiResponse getProductBasicDetail();

    List<IdCodeName> getAllDerivedProducts();

    List<UnitDerivedProductMappingDto> getDerivedProductsMappingForUnit(Integer unitId);

    List<UnitDerivedProductMappingDto> getAllUnitsForProductMapping(Integer productId);

    ApiResponse updateDerivedMappingData(List<UnitDerivedProductMappingDto> request);

    List<UnitDerivedProductMappingDto> getDerivedProductCloningForUnits(Integer fromUnitId );

    List<UnitDerivedProductMappingDto> getUnitsCloningForProduct(Integer fromProductId);

    ApiResponse cloneDerivedProductMappings(List<UnitDerivedProductMappingDto> request, Integer valueId, String valueType);

    ApiResponse getRegionProductPackagings(RPPMappingRequest request) throws SumoException;

    Map<Integer, List<IdCodeName>> getProductPackagingMapping(RPPMappingRequest request);

    void mapRegionProductPackaging(List<RegionProductPackagingMapping> request);


}
